import json
import os
from datetime import datetime
from enum import Enum
from utils.config_manager import config_manager

class AccountStatus(Enum):
    WAITING = "waiting"
    RUNNING = "running" 
    COOLDOWN = "cooldown"
    ERROR = "error"

class AccountService:
    def __init__(self):
        self.status_file = "runtime/account_status.json"
        self.ensure_files_exist()
    
    def ensure_files_exist(self):
        """确保文件和目录存在"""
        os.makedirs("config", exist_ok=True)
        os.makedirs("runtime", exist_ok=True)
        
        # 确保accounts配置存在
        if not config_manager.get("accounts"):
            config_manager.update_accounts_data({"accounts": [], "settings": {}})
        
        if not os.path.exists(self.status_file):
            self.save_status({})
    
    def load_accounts(self):
        """加载账户配置 - 使用缓存"""
        return config_manager.get("accounts") or {"accounts": [], "settings": {}}
    
    def save_accounts(self, data):
        """保存账户配置 - 更新缓存并写入文件"""
        config_manager.update_accounts_data(data)
    
    def load_status(self):
        """加载账户状态"""
        try:
            with open(self.status_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    
    def save_status(self, status_data):
        """保存账户状态"""
        with open(self.status_file, 'w', encoding='utf-8') as f:
            json.dump(status_data, f, indent=2, ensure_ascii=False)
    
    def add_account(self, username, password, pin_code, role, role_id):
        """添加单个账户"""
        data = self.load_accounts()
        accounts = data.get("accounts", [])
        
        max_id = max([acc.get("id", 0) for acc in accounts], default=0)
        new_account = {
            "id": max_id + 1,
            "username": username,
            "password": password,
            "pin_code": pin_code,
            "role": role,
            "role_id": role_id
        }
        
        accounts.append(new_account)
        data["accounts"] = accounts
        self.save_accounts(data)
        
        self.init_account_status(new_account["id"])
        return new_account
    
    def init_account_status(self, account_id):
        """初始化账户状态 - 包含enabled和created_time"""
        status_data = self.load_status()
        status_data[str(account_id)] = {
            "enabled": True,  # 移到状态文件
            "status": AccountStatus.WAITING.value,
            "last_login": None,
            "last_logout": None,
            "current_runtime": 0,
            "work_start_time": None,
            "cooldown_start_time": None,
            "error_count": 0,
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 移到状态文件
        }
        self.save_status(status_data)
    
    def import_accounts_from_file(self, file_path):
        """从文件导入账户"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            existing_data = self.load_accounts()
            existing_accounts = existing_data.get('accounts', [])
            
            existing_combinations = set()
            for acc in existing_accounts:
                combination = (acc.get('username'), acc.get('role_id'))
                existing_combinations.add(combination)
            
            imported_accounts = []
            skipped_count = 0
            
            for account_data in import_data.get('accounts', []):
                new_combination = (account_data.get('username'), account_data.get('role_id'))
                
                if new_combination in existing_combinations:
                    skipped_count += 1
                    print(f"[导入] 跳过重复账户: {account_data.get('username')} - {account_data.get('role_id')}")
                    continue
                
                account = self.add_account(
                    username=account_data['username'],
                    password=account_data['password'],
                    pin_code=account_data['pin_code'],
                    role=account_data['role'],
                    role_id=account_data['role_id']
                )
                imported_accounts.append(account)
                existing_combinations.add(new_combination)
            
            return {
                'imported_accounts': imported_accounts,
                'imported_count': len(imported_accounts),
                'skipped_count': skipped_count
            }
        except Exception as e:
            print(f"[导入] 导入失败: {str(e)}")
            return None
    
    def export_accounts_to_file(self, file_path, include_status=False):
        """导出账户"""
        data = self.load_accounts()
        
        if include_status:
            status_data = self.load_status()
            export_data = {
                "accounts": data.get("accounts", []),
                "status": status_data,
                "settings": data.get("settings", {})
            }
        else:
            export_data = {
                "accounts": data.get("accounts", [])
            }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        return True
    
    def get_account_with_status(self, account_id):
        """获取账户配置和状态的合并数据"""
        accounts_data = self.load_accounts()
        status_data = self.load_status()
        
        for account in accounts_data.get("accounts", []):
            if account["id"] == account_id:
                account_status = status_data.get(str(account_id), {})
                return {**account, **account_status}
        
        return None

    def get_account_by_id(self, account_id):
        """根据ID获取账户信息 - 使用缓存"""
        return config_manager.get_account_by_id(account_id)
    
    def delete_account(self, account_id):
        """删除账户"""
        try:
            data = self.load_accounts()
            accounts = data.get("accounts", [])
            
            for i, account in enumerate(accounts):
                if account.get("id") == account_id:
                    del accounts[i]
                    data["accounts"] = accounts
                    self.save_accounts(data)
                    
                    # 删除状态信息
                    status_data = self.load_status()
                    if str(account_id) in status_data:
                        del status_data[str(account_id)]
                        self.save_status(status_data)
                    
                    return True
            return False
        except Exception as e:
            print(f"删除账户失败: {str(e)}")
            return False

    def update_account(self, account_id, updates):
        """更新账户信息 - 智能分离更新"""
        basic_fields = {'username', 'password', 'pin_code', 'role', 'role_id'}
        status_fields = {'enabled', 'status', 'last_login', 'last_logout', 
                        'current_runtime', 'work_start_time', 'cooldown_start_time', 
                        'error_count', 'created_time'}
        
        basic_updates = {k: v for k, v in updates.items() if k in basic_fields}
        status_updates = {k: v for k, v in updates.items() if k in status_fields}
        
        success = True
        
        # 更新基本信息
        if basic_updates:
            data = self.load_accounts()
            accounts = data.get("accounts", [])
            for account in accounts:
                if account.get("id") == account_id:
                    account.update(basic_updates)
                    self.save_accounts(data)
                    break
            else:
                success = False
        
        # 更新状态信息
        if status_updates:
            status_data = self.load_status()
            if str(account_id) in status_data:
                status_data[str(account_id)].update(status_updates)
                self.save_status(status_data)
            else:
                success = False
        
        return success

