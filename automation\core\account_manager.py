class AccountManager:
    """账号管理器，负责账号的核心逻辑"""
    
    def __init__(self, config_service):
        self.config_service = config_service
        
    def get_all_accounts(self):
        """获取所有账号"""
        return self.config_service.get_all_accounts()
    
    def add_account(self, account_data):
        """添加账号"""
        return self.config_service.add_account(account_data)
    
    def update_account(self, account_id, updates):
        """更新账号"""
        return self.config_service.update_account(account_id, updates)
    
    def delete_account(self, account_id):
        """删除账号"""
        return self.config_service.delete_account(account_id)
    
    def get_account_by_id(self, account_id):
        """根据ID获取账号"""
        return self.config_service.get_account(account_id)
