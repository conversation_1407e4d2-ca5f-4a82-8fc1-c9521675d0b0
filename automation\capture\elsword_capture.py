import cv2
import os.path
from automation.capture.recognizable_capture import RecognizableCapture
from automation.myutils.configutils import resource_path

class ElswordCapture(RecognizableCapture):
    """艾尔之光游戏识别捕获器"""
    
    def __init__(self, window_name="Elsword"):
        super().__init__()
        self.window_name = window_name
        self._load_elsword_templates()
    
    def _load_elsword_templates(self):
        """加载艾尔之光游戏模板"""
        template_path = os.path.join(resource_path, 'template', 'elsword')
        
        # 游戏界面元素
        self.icon_login_button = cv2.imread(os.path.join(template_path, 'btn_login.png'), cv2.IMREAD_GRAYSCALE)
        self.icon_character_select = cv2.imread(os.path.join(template_path, 'character_select.png'), cv2.IMREAD_GRAYSCALE)
        self.icon_dungeon_enter = cv2.imread(os.path.join(template_path, 'btn_dungeon_enter.png'), cv2.IMREAD_GRAYSCALE)
    
 
