{"base_code": {"magic": "pir", "trainable": true, "version": 1}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "conv2d_transpose_1.b_0"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_1.w_0"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 1, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.w_2"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.w_1"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.b_0"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.w_0"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_0.b_0"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_0.w_0"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 24, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.w_2"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.w_1"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.b_0"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.w_0"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_159.w_0"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_158.b_0"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_158.w_0"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 6, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_157.b_0"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_157.w_0"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_156.w_0"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_152.b_0"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_152.w_0"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 6, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_151.b_0"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_151.w_0"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_150.w_0"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_146.b_0"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_146.w_0"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 6, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_145.b_0"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_145.w_0"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_144.w_0"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_140.b_0"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_140.w_0"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 6, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_139.b_0"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_139.w_0"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [6, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_138.w_0"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_155.b_0"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_155.w_0"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_154.b_0"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_154.w_0"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_153.w_0"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 360, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_149.b_0"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_149.w_0"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_148.b_0"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_148.w_0"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_147.w_0"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 42, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_143.b_0"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_143.w_0"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_142.b_0"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_142.w_0"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_141.w_0"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 18, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_137.b_0"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_137.w_0"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 24, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_136.b_0"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_136.w_0"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_135.w_0"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 12, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_134.b_0"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_134.w_0"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_133.b_0"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [42], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_133.w_0"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [42, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_132.b_0"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [18], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_132.w_0"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [18, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_131.b_0"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [12], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_131.w_0"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [12, 48, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_188.b_0"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_188.w_0"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_55.w_1"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_55.w_0"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_54.w_1"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_54.w_0"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_187.b_0"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_187.w_0"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_53.w_1"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_53.w_0"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_52.w_1"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_52.w_0"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_186.b_0"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_186.w_0"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_51.w_1"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_51.w_0"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_50.w_1"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_50.w_0"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_185.b_0"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_185.w_0"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_49.w_1"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_49.w_0"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_48.w_1"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_48.w_0"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_184.b_0"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_184.w_0"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_47.w_1"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_47.w_0"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_46.w_1"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_46.w_0"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_108.b_0"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_108.w_0"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_107.b_0"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_107.w_0"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_183.b_0"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_183.w_0"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_45.w_1"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_45.w_0"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_44.w_1"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_44.w_0"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_182.b_0"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_182.w_0"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_43.w_1"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_43.w_0"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_42.w_1"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_42.w_0"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_97.b_0"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_97.w_0"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 48, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_96.b_0"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_96.w_0"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_181.b_0"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_181.w_0"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_40.w_1"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_40.w_0"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_180.b_0"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_180.w_0"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_39.w_1"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_39.w_0"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_38.w_1"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_38.w_0"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_179.b_0"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_179.w_0"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_37.w_1"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_37.w_0"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_36.w_1"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_36.w_0"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_178.b_0"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_178.w_0"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_35.w_1"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_35.w_0"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_34.w_1"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_34.w_0"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_177.b_0"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_177.w_0"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_33.w_1"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_33.w_0"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_32.w_1"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_32.w_0"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_176.b_0"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_176.w_0"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_31.w_1"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_31.w_0"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_30.w_1"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_30.w_0"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_175.b_0"], "DA": [], "O": {"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_175.w_0"], "DA": [], "O": {"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_29.w_1"], "DA": [], "O": {"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_29.w_0"], "DA": [], "O": {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_28.w_1"], "DA": [], "O": {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_28.w_0"], "DA": [], "O": {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_174.b_0"], "DA": [], "O": {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_174.w_0"], "DA": [], "O": {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_27.w_1"], "DA": [], "O": {"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_27.w_0"], "DA": [], "O": {"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_26.w_1"], "DA": [], "O": {"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_26.w_0"], "DA": [], "O": {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_173.b_0"], "DA": [], "O": {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_173.w_0"], "DA": [], "O": {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_25.w_1"], "DA": [], "O": {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_25.w_0"], "DA": [], "O": {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_24.w_1"], "DA": [], "O": {"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_24.w_0"], "DA": [], "O": {"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_172.b_0"], "DA": [], "O": {"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_172.w_0"], "DA": [], "O": {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_23.w_1"], "DA": [], "O": {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_23.w_0"], "DA": [], "O": {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_22.w_1"], "DA": [], "O": {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_22.w_0"], "DA": [], "O": {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_171.b_0"], "DA": [], "O": {"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_171.w_0"], "DA": [], "O": {"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_20.w_1"], "DA": [], "O": {"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_20.w_0"], "DA": [], "O": {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_170.b_0"], "DA": [], "O": {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_170.w_0"], "DA": [], "O": {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_19.w_1"], "DA": [], "O": {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_19.w_0"], "DA": [], "O": {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_18.w_1"], "DA": [], "O": {"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_18.w_0"], "DA": [], "O": {"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_169.b_0"], "DA": [], "O": {"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_169.w_0"], "DA": [], "O": {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_17.w_1"], "DA": [], "O": {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_17.w_0"], "DA": [], "O": {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_16.w_1"], "DA": [], "O": {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_16.w_0"], "DA": [], "O": {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_168.b_0"], "DA": [], "O": {"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_168.w_0"], "DA": [], "O": {"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 48, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_15.w_1"], "DA": [], "O": {"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_15.w_0"], "DA": [], "O": {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_14.w_1"], "DA": [], "O": {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_14.w_0"], "DA": [], "O": {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_167.b_0"], "DA": [], "O": {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_167.w_0"], "DA": [], "O": {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_12.w_1"], "DA": [], "O": {"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_12.w_0"], "DA": [], "O": {"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_166.b_0"], "DA": [], "O": {"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_166.w_0"], "DA": [], "O": {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_11.w_1"], "DA": [], "O": {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_11.w_0"], "DA": [], "O": {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_10.w_1"], "DA": [], "O": {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_10.w_0"], "DA": [], "O": {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_165.b_0"], "DA": [], "O": {"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_165.w_0"], "DA": [], "O": {"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_9.w_1"], "DA": [], "O": {"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_9.w_0"], "DA": [], "O": {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_8.w_1"], "DA": [], "O": {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_8.w_0"], "DA": [], "O": {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_164.b_0"], "DA": [], "O": {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_164.w_0"], "DA": [], "O": {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_7.w_1"], "DA": [], "O": {"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_7.w_0"], "DA": [], "O": {"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_6.w_1"], "DA": [], "O": {"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_6.w_0"], "DA": [], "O": {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_163.b_0"], "DA": [], "O": {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_163.w_0"], "DA": [], "O": {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_4.w_1"], "DA": [], "O": {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_4.w_0"], "DA": [], "O": {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_162.b_0"], "DA": [], "O": {"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_162.w_0"], "DA": [], "O": {"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 16, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_3.w_1"], "DA": [], "O": {"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_3.w_0"], "DA": [], "O": {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_2.w_1"], "DA": [], "O": {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_2.w_0"], "DA": [], "O": {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_161.b_0"], "DA": [], "O": {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_161.w_0"], "DA": [], "O": {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_1.w_1"], "DA": [], "O": {"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_1.w_0"], "DA": [], "O": {"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_0.w_1"], "DA": [], "O": {"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_0.w_0"], "DA": [], "O": {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2"], "DA": [], "O": {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1"], "DA": [], "O": {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0"], "DA": [], "O": {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0"], "DA": [], "O": {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0"], "DA": [], "O": {"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 3, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "x"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, -1, -1]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 235}, {"%": 234}], "O": [{"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 236}, {"%": 231}, {"%": 230}, {"%": 233}, {"%": 232}], "O": [{"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}, {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 16}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 237}, {"%": 225}], "O": [{"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 224}, {"%": 244}], "O": [{"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 16, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 243}, {"%": 245}], "O": [{"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 229}, {"%": 246}], "O": [{"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 247}, {"%": 228}], "O": [{"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 248}], "O": [{"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 227}, {"%": 249}], "O": [{"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 250}, {"%": 226}], "O": [{"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 251}, {"%": 219}], "O": [{"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 218}, {"%": 253}], "O": [{"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 252}, {"%": 254}], "O": [{"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 223}, {"%": 255}], "O": [{"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 256}, {"%": 222}], "O": [{"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 257}], "O": [{"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 221}, {"%": 258}], "O": [{"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 259}, {"%": 220}], "O": [{"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 32}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 260}, {"%": 215}], "O": [{"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 214}, {"%": 262}], "O": [{"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 261}, {"%": 263}], "O": [{"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 217}, {"%": 264}], "O": [{"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 265}, {"%": 216}], "O": [{"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 266}, {"%": 209}], "O": [{"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 208}, {"%": 268}], "O": [{"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 267}, {"%": 269}], "O": [{"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 213}, {"%": 270}], "O": [{"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 271}, {"%": 212}], "O": [{"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 272}], "O": [{"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 211}, {"%": 273}], "O": [{"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 274}, {"%": 210}], "O": [{"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 48}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 275}, {"%": 203}], "O": [{"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 202}, {"%": 277}], "O": [{"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 276}, {"%": 278}], "O": [{"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 207}, {"%": 279}], "O": [{"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 280}, {"%": 206}], "O": [{"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 281}], "O": [{"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 205}, {"%": 282}], "O": [{"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 283}, {"%": 204}], "O": [{"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 284}, {"%": 197}], "O": [{"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 196}, {"%": 286}], "O": [{"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 285}, {"%": 287}], "O": [{"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 201}, {"%": 288}], "O": [{"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 289}, {"%": 200}], "O": [{"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 290}], "O": [{"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 199}, {"%": 291}], "O": [{"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 292}, {"%": 198}], "O": [{"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 48}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 293}, {"%": 193}], "O": [{"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 192}, {"%": 295}], "O": [{"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 294}, {"%": 296}], "O": [{"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 195}, {"%": 297}], "O": [{"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 298}, {"%": 194}], "O": [{"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 299}, {"%": 187}], "O": [{"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 186}, {"%": 301}], "O": [{"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 300}, {"%": 302}], "O": [{"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 191}, {"%": 303}], "O": [{"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 304}, {"%": 190}], "O": [{"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 305}], "O": [{"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 189}, {"%": 306}], "O": [{"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 307}, {"%": 188}], "O": [{"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 96}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 308}, {"%": 181}], "O": [{"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 180}, {"%": 310}], "O": [{"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 309}, {"%": 311}], "O": [{"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 185}, {"%": 312}], "O": [{"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 313}, {"%": 184}], "O": [{"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 314}], "O": [{"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 183}, {"%": 315}], "O": [{"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 316}, {"%": 182}], "O": [{"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 317}, {"%": 175}], "O": [{"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 174}, {"%": 319}], "O": [{"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 318}, {"%": 320}], "O": [{"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 179}, {"%": 321}], "O": [{"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 322}, {"%": 178}], "O": [{"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 323}], "O": [{"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 177}, {"%": 324}], "O": [{"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 325}, {"%": 176}], "O": [{"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 96}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 326}, {"%": 171}], "O": [{"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 170}, {"%": 328}], "O": [{"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 327}, {"%": 329}], "O": [{"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 173}, {"%": 330}], "O": [{"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 331}, {"%": 172}], "O": [{"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 332}, {"%": 165}], "O": [{"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 164}, {"%": 334}], "O": [{"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 333}, {"%": 335}], "O": [{"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 169}, {"%": 336}], "O": [{"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 337}, {"%": 168}], "O": [{"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 338}], "O": [{"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 167}, {"%": 339}], "O": [{"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 340}, {"%": 166}], "O": [{"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 341}, {"%": 159}], "O": [{"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 158}, {"%": 343}], "O": [{"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 342}, {"%": 344}], "O": [{"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 163}, {"%": 345}], "O": [{"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 346}, {"%": 162}], "O": [{"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 347}], "O": [{"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 161}, {"%": 348}], "O": [{"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 349}, {"%": 160}], "O": [{"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 350}, {"%": 153}], "O": [{"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 152}, {"%": 352}], "O": [{"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 351}, {"%": 353}], "O": [{"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 157}, {"%": 354}], "O": [{"%": 355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 355}, {"%": 156}], "O": [{"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 356}], "O": [{"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 155}, {"%": 357}], "O": [{"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 358}, {"%": 154}], "O": [{"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 359}, {"%": 147}], "O": [{"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 146}, {"%": 361}], "O": [{"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 360}, {"%": 362}], "O": [{"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 151}, {"%": 363}], "O": [{"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 364}, {"%": 150}], "O": [{"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 365}], "O": [{"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 149}, {"%": 366}], "O": [{"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 367}, {"%": 148}], "O": [{"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 368}, {"%": 141}], "O": [{"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 140}, {"%": 370}], "O": [{"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 369}, {"%": 371}], "O": [{"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 145}, {"%": 372}], "O": [{"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 373}, {"%": 144}], "O": [{"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 374}], "O": [{"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 143}, {"%": 375}], "O": [{"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 376}, {"%": 142}], "O": [{"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 377}, {"%": 135}], "O": [{"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 134}, {"%": 379}], "O": [{"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 378}, {"%": 380}], "O": [{"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 139}, {"%": 381}], "O": [{"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 382}, {"%": 138}], "O": [{"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 383}], "O": [{"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 137}, {"%": 384}], "O": [{"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 385}, {"%": 136}], "O": [{"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 386}, {"%": 129}], "O": [{"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 128}, {"%": 388}], "O": [{"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 387}, {"%": 389}], "O": [{"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 133}, {"%": 390}], "O": [{"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 391}, {"%": 132}], "O": [{"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 392}], "O": [{"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 131}, {"%": 393}], "O": [{"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 394}, {"%": 130}], "O": [{"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 395}, {"%": 123}], "O": [{"%": 396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 122}, {"%": 397}], "O": [{"%": 398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 396}, {"%": 398}], "O": [{"%": 399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 127}, {"%": 399}], "O": [{"%": 400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 400}, {"%": 126}], "O": [{"%": 401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 401}], "O": [{"%": 402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 125}, {"%": 402}], "O": [{"%": 403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 403}, {"%": 124}], "O": [{"%": 404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 404}, {"%": 117}], "O": [{"%": 405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 116}, {"%": 406}], "O": [{"%": 407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 405}, {"%": 407}], "O": [{"%": 408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 121}, {"%": 408}], "O": [{"%": 409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 409}, {"%": 120}], "O": [{"%": 410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 410}], "O": [{"%": 411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 119}, {"%": 411}], "O": [{"%": 412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 412}, {"%": 118}], "O": [{"%": 413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 413}, {"%": 113}], "O": [{"%": 414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 112}, {"%": 415}], "O": [{"%": 416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 414}, {"%": 416}], "O": [{"%": 417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 115}, {"%": 417}], "O": [{"%": 418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 418}, {"%": 114}], "O": [{"%": 419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 419}, {"%": 420}], "O": [{"%": 421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 421}, {"%": 111}], "O": [{"%": 422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 110}, {"%": 423}], "O": [{"%": 424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 422}, {"%": 424}], "O": [{"%": 425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/ReLU/"}, "N": "struct_name"}], "I": [{"%": 425}], "O": [{"%": 426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 426}, {"%": 109}], "O": [{"%": 427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 108}, {"%": 428}], "O": [{"%": 429, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 427}, {"%": 429}], "O": [{"%": 430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.16666670143604279}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Hardsigmoid/"}, "N": "struct_name"}], "I": [{"%": 430}], "O": [{"%": 431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/"}, "N": "struct_name"}], "I": [{"%": 419}, {"%": 431}], "O": [{"%": 432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 432}, {"%": 103}], "O": [{"%": 433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 102}, {"%": 434}], "O": [{"%": 435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 433}, {"%": 435}], "O": [{"%": 436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 107}, {"%": 436}], "O": [{"%": 437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 437}, {"%": 106}], "O": [{"%": 438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 438}], "O": [{"%": 439, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 105}, {"%": 439}], "O": [{"%": 440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 440}, {"%": 104}], "O": [{"%": 441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 441}, {"%": 97}], "O": [{"%": 442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 96}, {"%": 443}], "O": [{"%": 444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 442}, {"%": 444}], "O": [{"%": 445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 101}, {"%": 445}], "O": [{"%": 446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 446}, {"%": 100}], "O": [{"%": 447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 447}], "O": [{"%": 448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 99}, {"%": 448}], "O": [{"%": 449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 449}, {"%": 98}], "O": [{"%": 450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 450}, {"%": 451}], "O": [{"%": 452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 452}, {"%": 95}], "O": [{"%": 453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 94}, {"%": 454}], "O": [{"%": 455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 453}, {"%": 455}], "O": [{"%": 456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/ReLU/"}, "N": "struct_name"}], "I": [{"%": 456}], "O": [{"%": 457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 457}, {"%": 93}], "O": [{"%": 458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 92}, {"%": 459}], "O": [{"%": 460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 458}, {"%": 460}], "O": [{"%": 461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.16666670143604279}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Hardsigmoid/"}, "N": "struct_name"}], "I": [{"%": 461}], "O": [{"%": 462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/"}, "N": "struct_name"}], "I": [{"%": 450}, {"%": 462}], "O": [{"%": 463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 463}, {"%": 87}], "O": [{"%": 464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 86}, {"%": 465}], "O": [{"%": 466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 464}, {"%": 466}], "O": [{"%": 467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 91}, {"%": 467}], "O": [{"%": 468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 468}, {"%": 90}], "O": [{"%": 469, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 469}], "O": [{"%": 470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 89}, {"%": 470}], "O": [{"%": 471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 471}, {"%": 88}], "O": [{"%": 472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 472}, {"%": 81}], "O": [{"%": 473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 80}, {"%": 474}], "O": [{"%": 475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 473}, {"%": 475}], "O": [{"%": 476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 85}, {"%": 476}], "O": [{"%": 477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 477}, {"%": 84}], "O": [{"%": 478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 478}], "O": [{"%": 479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 83}, {"%": 479}], "O": [{"%": 480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 480}, {"%": 82}], "O": [{"%": 481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 481}, {"%": 75}], "O": [{"%": 482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 74}, {"%": 483}], "O": [{"%": 484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 482}, {"%": 484}], "O": [{"%": 485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 79}, {"%": 485}], "O": [{"%": 486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 486}, {"%": 78}], "O": [{"%": 487, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 487}], "O": [{"%": 488, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 77}, {"%": 488}], "O": [{"%": 489, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 489}, {"%": 76}], "O": [{"%": 490, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 490}, {"%": 69}], "O": [{"%": 491, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 492, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 68}, {"%": 492}], "O": [{"%": 493, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 491}, {"%": 493}], "O": [{"%": 494, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 73}, {"%": 494}], "O": [{"%": 495, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 495}, {"%": 72}], "O": [{"%": 496, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 496}], "O": [{"%": 497, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 71}, {"%": 497}], "O": [{"%": 498, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 498}, {"%": 70}], "O": [{"%": 499, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 499}, {"%": 63}], "O": [{"%": 500, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 501, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 62}, {"%": 501}], "O": [{"%": 502, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 384, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 500}, {"%": 502}], "O": [{"%": 503, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 67}, {"%": 503}], "O": [{"%": 504, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 504}, {"%": 66}], "O": [{"%": 505, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 505}], "O": [{"%": 506, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 65}, {"%": 506}], "O": [{"%": 507, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 507}, {"%": 64}], "O": [{"%": 508, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 293}, {"%": 61}], "O": [{"%": 509, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 12, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 510, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 60}, {"%": 510}], "O": [{"%": 511, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 12, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 509}, {"%": 511}], "O": [{"%": 512, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 12, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 326}, {"%": 59}], "O": [{"%": 513, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 18, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 514, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 58}, {"%": 514}], "O": [{"%": 515, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 18, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 513}, {"%": 515}], "O": [{"%": 516, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 18, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 413}, {"%": 57}], "O": [{"%": 517, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 42, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 518, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 56}, {"%": 518}], "O": [{"%": 519, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 42, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 517}, {"%": 519}], "O": [{"%": 520, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 42, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 508}, {"%": 55}], "O": [{"%": 521, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 360, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_3/"}, "N": "struct_name"}], "I": [], "O": [{"%": 522, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 54}, {"%": 522}], "O": [{"%": 523, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 360, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 521}, {"%": 523}], "O": [{"%": 524, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 360, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 524}, {"%": 38}], "O": [{"%": 525, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 526, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 525}, {"%": 526}], "O": [{"%": 527, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 527}, {"%": 37}], "O": [{"%": 528, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 529, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 36}, {"%": 529}], "O": [{"%": 530, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 528}, {"%": 530}], "O": [{"%": 531, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/"}, "N": "struct_name"}], "I": [{"%": 531}], "O": [{"%": 532, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 532}, {"%": 35}], "O": [{"%": 533, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 534, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 34}, {"%": 534}], "O": [{"%": 535, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 533}, {"%": 535}], "O": [{"%": 536, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/"}, "N": "struct_name"}], "I": [{"%": 536}], "O": [{"%": 537, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/SEModule/"}, "N": "struct_name"}], "I": [{"%": 525}, {"%": 537}], "O": [{"%": 538, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer/"}, "N": "struct_name"}], "I": [{"%": 525}, {"%": 538}], "O": [{"%": 539, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 520}, {"%": 43}], "O": [{"%": 540, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 541, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 540}, {"%": 541}], "O": [{"%": 542, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 542}, {"%": 42}], "O": [{"%": 543, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 544, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 41}, {"%": 544}], "O": [{"%": 545, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 543}, {"%": 545}], "O": [{"%": 546, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/"}, "N": "struct_name"}], "I": [{"%": 546}], "O": [{"%": 547, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 547}, {"%": 40}], "O": [{"%": 548, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 549, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 39}, {"%": 549}], "O": [{"%": 550, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 548}, {"%": 550}], "O": [{"%": 551, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/"}, "N": "struct_name"}], "I": [{"%": 551}], "O": [{"%": 552, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/SEModule/"}, "N": "struct_name"}], "I": [{"%": 540}, {"%": 552}], "O": [{"%": 553, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_1/"}, "N": "struct_name"}], "I": [{"%": 540}, {"%": 553}], "O": [{"%": 554, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 516}, {"%": 48}], "O": [{"%": 555, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 556, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 555}, {"%": 556}], "O": [{"%": 557, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 557}, {"%": 47}], "O": [{"%": 558, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 559, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 46}, {"%": 559}], "O": [{"%": 560, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 558}, {"%": 560}], "O": [{"%": 561, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/"}, "N": "struct_name"}], "I": [{"%": 561}], "O": [{"%": 562, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 562}, {"%": 45}], "O": [{"%": 563, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 564, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 44}, {"%": 564}], "O": [{"%": 565, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 563}, {"%": 565}], "O": [{"%": 566, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/"}, "N": "struct_name"}], "I": [{"%": 566}], "O": [{"%": 567, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/SEModule/"}, "N": "struct_name"}], "I": [{"%": 555}, {"%": 567}], "O": [{"%": 568, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_2/"}, "N": "struct_name"}], "I": [{"%": 555}, {"%": 568}], "O": [{"%": 569, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 512}, {"%": 53}], "O": [{"%": 570, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 571, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 570}, {"%": 571}], "O": [{"%": 572, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 572}, {"%": 52}], "O": [{"%": 573, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 574, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 51}, {"%": 574}], "O": [{"%": 575, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 573}, {"%": 575}], "O": [{"%": 576, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/"}, "N": "struct_name"}], "I": [{"%": 576}], "O": [{"%": 577, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 577}, {"%": 50}], "O": [{"%": 578, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 579, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 49}, {"%": 579}], "O": [{"%": 580, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 578}, {"%": 580}], "O": [{"%": 581, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/"}, "N": "struct_name"}], "I": [{"%": 581}], "O": [{"%": 582, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/SEModule/"}, "N": "struct_name"}], "I": [{"%": 570}, {"%": 582}], "O": [{"%": 583, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_3/"}, "N": "struct_name"}], "I": [{"%": 570}, {"%": 583}], "O": [{"%": 584, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 539}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 585, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 554}, {"%": 585}], "O": [{"%": 586, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 586}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 587, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 569}, {"%": 587}], "O": [{"%": 588, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 588}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 589, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 584}, {"%": 589}], "O": [{"%": 590, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 539}, {"%": 18}], "O": [{"%": 591, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 592, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 591}, {"%": 592}], "O": [{"%": 593, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 593}, {"%": 17}], "O": [{"%": 594, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 595, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 16}, {"%": 595}], "O": [{"%": 596, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 594}, {"%": 596}], "O": [{"%": 597, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/"}, "N": "struct_name"}], "I": [{"%": 597}], "O": [{"%": 598, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 598}, {"%": 15}], "O": [{"%": 599, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 600, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 14}, {"%": 600}], "O": [{"%": 601, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 599}, {"%": 601}], "O": [{"%": 602, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/"}, "N": "struct_name"}], "I": [{"%": 602}], "O": [{"%": 603, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/SEModule/"}, "N": "struct_name"}], "I": [{"%": 591}, {"%": 603}], "O": [{"%": 604, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_4/"}, "N": "struct_name"}], "I": [{"%": 591}, {"%": 604}], "O": [{"%": 605, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 586}, {"%": 23}], "O": [{"%": 606, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 607, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 606}, {"%": 607}], "O": [{"%": 608, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 608}, {"%": 22}], "O": [{"%": 609, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 610, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 21}, {"%": 610}], "O": [{"%": 611, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 609}, {"%": 611}], "O": [{"%": 612, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/"}, "N": "struct_name"}], "I": [{"%": 612}], "O": [{"%": 613, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 613}, {"%": 20}], "O": [{"%": 614, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 615, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 19}, {"%": 615}], "O": [{"%": 616, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 614}, {"%": 616}], "O": [{"%": 617, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/"}, "N": "struct_name"}], "I": [{"%": 617}], "O": [{"%": 618, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/SEModule/"}, "N": "struct_name"}], "I": [{"%": 606}, {"%": 618}], "O": [{"%": 619, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_5/"}, "N": "struct_name"}], "I": [{"%": 606}, {"%": 619}], "O": [{"%": 620, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 588}, {"%": 28}], "O": [{"%": 621, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 622, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 621}, {"%": 622}], "O": [{"%": 623, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 623}, {"%": 27}], "O": [{"%": 624, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 625, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 26}, {"%": 625}], "O": [{"%": 626, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 624}, {"%": 626}], "O": [{"%": 627, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/"}, "N": "struct_name"}], "I": [{"%": 627}], "O": [{"%": 628, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 628}, {"%": 25}], "O": [{"%": 629, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 630, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 24}, {"%": 630}], "O": [{"%": 631, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 629}, {"%": 631}], "O": [{"%": 632, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/"}, "N": "struct_name"}], "I": [{"%": 632}], "O": [{"%": 633, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/SEModule/"}, "N": "struct_name"}], "I": [{"%": 621}, {"%": 633}], "O": [{"%": 634, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_6/"}, "N": "struct_name"}], "I": [{"%": 621}, {"%": 634}], "O": [{"%": 635, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 590}, {"%": 33}], "O": [{"%": 636, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 637, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 637}], "O": [{"%": 638, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 638}, {"%": 32}], "O": [{"%": 639, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 640, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 31}, {"%": 640}], "O": [{"%": 641, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 639}, {"%": 641}], "O": [{"%": 642, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/"}, "N": "struct_name"}], "I": [{"%": 642}], "O": [{"%": 643, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 6, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 643}, {"%": 30}], "O": [{"%": 644, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 645, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 29}, {"%": 645}], "O": [{"%": 646, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 644}, {"%": 646}], "O": [{"%": 647, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.20000000298023224}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/"}, "N": "struct_name"}], "I": [{"%": 647}], "O": [{"%": 648, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/SEModule/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 648}], "O": [{"%": 649, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/RSELayer_7/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 649}], "O": [{"%": 650, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 8.0}, {"#": "0.a_f32", "D": 8.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 605}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 651, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 4.0}, {"#": "0.a_f32", "D": 4.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 620}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 652, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 635}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 653, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [], "O": [{"%": 654, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 651}, {"%": 652}, {"%": 653}, {"%": 650}], "O": [{"%": 655, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/RSEFPN/"}, "N": "struct_name"}], "I": [{"%": 655}, {"%": 654}], "O": [{"%": 656, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 656}, {"%": 13}], "O": [{"%": 657, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/BatchNorm/"}, "N": "struct_name"}], "I": [{"%": 657}, {"%": 10}, {"%": 9}, {"%": 12}, {"%": 11}], "O": [{"%": 658, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}, {"%": 659, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 660, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 661, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 662, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 663, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/BatchNorm/"}, "N": "struct_name"}], "I": [{"%": 658}], "O": [{"%": 664, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": []}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [], "O": [{"%": 665, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [0], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d_transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_array", "D": []}, "N": "output_padding"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [{"%": 664}, {"%": 8}, {"%": 665}], "O": [{"%": 666, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 24}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [], "O": [{"%": 667, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [{"%": 7}, {"%": 667}], "O": [{"%": 668, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 24, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [{"%": 666}, {"%": 668}], "O": [{"%": 669, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/BatchNorm_1/"}, "N": "struct_name"}], "I": [{"%": 669}, {"%": 4}, {"%": 3}, {"%": 6}, {"%": 5}], "O": [{"%": 670, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}, {"%": 671, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 672, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 673, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 674, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [24], "NCHW", [], 0]}}, {"%": 675, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/BatchNorm_1/"}, "N": "struct_name"}], "I": [{"%": 670}], "O": [{"%": 676, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 24, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": []}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 677, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [0], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d_transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_array", "D": []}, "N": "output_padding"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [{"%": 676}, {"%": 2}, {"%": 677}], "O": [{"%": 678, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 679, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [{"%": 1}, {"%": 679}], "O": [{"%": 680, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 1, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [{"%": 678}, {"%": 680}], "O": [{"%": 681, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/DBHead/Head/"}, "N": "struct_name"}], "I": [{"%": 681}], "O": [{"%": 682, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 682}], "O": [{"%": 683, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}