"""
Sandboxie 工具函数
提供便捷的 Sandboxie 操作接口，基于 HandleManager
"""
import time
from mylogger.MyLogger import MyLogger
from automation.core.handle_manager import handle_manager

logger = MyLogger('SandboxieUtils', save_log=True)

def click_sandbox_text(text="沙箱", match_all=False, mss_mode=False):
    """
    点击 Sandboxie 界面中的指定文本
    
    Args:
        text: 要查找的文本，默认为"沙箱"
        match_all: 是否匹配所有文本
        mss_mode: 是否使用 MSS 模式
    
    Returns:
        bool: 操作是否成功
    """
    try:
        # 获取实例（自动处理句柄刷新）
        sandboxie_capture, ocr_controller = handle_manager.get_sandboxie_instances()
        
        logger.info(f"使用句柄 {sandboxie_capture.hwnd} 执行OCR操作，查找文本: {text}")
        
        # 使用OCR查找并点击文字
        success = ocr_controller.find_text_and_bg_click(text, match_all=match_all, mss_mode=mss_mode)
        
        if success:
            logger.info(f"成功点击文本: {text}")
        else:
            logger.warning(f"未找到文本: {text}")
        
        return success
        
    except Exception as e:
        logger.error(f"点击文本操作失败: {e}")
        return False

def get_sandboxie_window_info():
    """
    获取 Sandboxie 窗口信息
    
    Returns:
        dict: 窗口信息字典
    """
    try:
        return handle_manager.get_current_handle_info()
    except Exception as e:
        logger.error(f"获取窗口信息失败: {e}")
        return {'error': str(e)}

def list_sandboxie_windows():
    """
    列出所有 Sandboxie 相关窗口
    
    Returns:
        list: 窗口信息列表
    """
    try:
        return handle_manager.list_sandboxie_windows()
    except Exception as e:
        logger.error(f"列出窗口失败: {e}")
        return []

def reset_sandboxie_instances():
    """
    重置 Sandboxie 实例
    当确定窗口已关闭时可以调用此函数
    """
    try:
        handle_manager.reset_instances()
        logger.info("Sandboxie 实例已重置")
        return True
    except Exception as e:
        logger.error(f"重置实例失败: {e}")
        return False

def ensure_sandboxie_ready():
    """
    确保 Sandboxie 准备就绪
    
    Returns:
        bool: 是否准备就绪
    """
    try:
        # 尝试获取实例
        sandboxie_capture, ocr_controller = handle_manager.get_sandboxie_instances()
        
        # 检查句柄是否有效
        if handle_manager.is_handle_valid(sandboxie_capture):
            logger.info(f"Sandboxie 已准备就绪，句柄: {sandboxie_capture.hwnd}")
            return True
        else:
            logger.warning("Sandboxie 句柄无效")
            return False
            
    except Exception as e:
        logger.error(f"检查 Sandboxie 状态失败: {e}")
        return False

def perform_sandboxie_operation(operation_func, *args, **kwargs):
    """
    执行 Sandboxie 操作的通用包装器
    自动处理句柄刷新和错误重试
    
    Args:
        operation_func: 要执行的操作函数
        *args: 操作函数的位置参数
        **kwargs: 操作函数的关键字参数
    
    Returns:
        操作函数的返回值
    """
    max_retries = 3
    retry_delay = 2
    
    for attempt in range(max_retries):
        try:
            # 确保 Sandboxie 准备就绪
            if not ensure_sandboxie_ready():
                if attempt < max_retries - 1:
                    logger.warning(f"Sandboxie 未准备就绪，第 {attempt + 1} 次重试...")
                    time.sleep(retry_delay)
                    continue
                else:
                    raise Exception("Sandboxie 无法准备就绪")
            
            # 执行操作
            result = operation_func(*args, **kwargs)
            logger.info(f"操作执行成功，第 {attempt + 1} 次尝试")
            return result
            
        except Exception as e:
            logger.error(f"操作执行失败，第 {attempt + 1} 次尝试: {e}")
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                # 重置实例以便下次重新创建
                reset_sandboxie_instances()
            else:
                logger.error("所有重试都失败了")
                raise e

def cleanup_sandboxie():
    """
    清理 Sandboxie 相关资源
    """
    try:
        handle_manager.cleanup()
        logger.info("Sandboxie 资源清理完成")
        return True
    except Exception as e:
        logger.error(f"清理 Sandboxie 资源失败: {e}")
        return False

# 便捷的操作函数
def create_new_sandbox():
    """
    创建新沙盒的便捷函数
    """
    def _create_operation():
        return click_sandbox_text("沙箱", match_all=False, mss_mode=False)
    
    return perform_sandboxie_operation(_create_operation)

def navigate_to_sandbox_menu():
    """
    导航到沙盒菜单的便捷函数
    """
    def _navigate_operation():
        # 可以根据需要添加更多导航步骤
        return click_sandbox_text("沙箱", match_all=False, mss_mode=False)
    
    return perform_sandboxie_operation(_navigate_operation)

# 调试和测试函数
def test_handle_refresh():
    """
    测试句柄刷新功能
    """
    print("=== 句柄刷新测试 ===")
    
    # 检查当前窗口
    print("\n1. 检查当前 Sandboxie 窗口:")
    windows = list_sandboxie_windows()
    if windows:
        print(f"找到 {len(windows)} 个 Sandboxie 窗口:")
        for i, win in enumerate(windows):
            print(f"  {i+1}. 句柄:{win['hwnd']}, 标题:'{win['title']}'")
    else:
        print("未找到 Sandboxie 窗口")
    
    # 测试操作
    print("\n2. 测试点击操作:")
    for i in range(3):
        print(f"\n第 {i+1} 次测试:")
        success = click_sandbox_text("沙箱")
        info = get_sandboxie_window_info()
        print(f"结果: {success}")
        print(f"句柄信息: {info.get('hwnd', 'N/A')}")
        time.sleep(2)
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_handle_refresh()
