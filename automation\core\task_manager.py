class TaskManager:
    """任务管理器，负责游戏任务的执行"""
    
    def __init__(self, config_service):
        self.config_service = config_service
        self.current_task = None
        self.running = False
        
    def get_all_tasks(self):
        """获取所有任务"""
        return self.config_service.get_tasks()
    
    def get_task(self, task_id):
        """获取特定任务"""
        return self.config_service.get_task(task_id)
    
    def start_task(self, task_id):
        """开始执行任务"""
        task = self.get_task(task_id)
        if not task:
            return False
        
        if not task.get("enabled", False):
            print(f"任务 {task_id} 未启用")
            return False
        
        self.current_task = task
        self.running = True
        print(f"开始执行任务: {task.get('name')}")
        
        # 这里实现任务执行逻辑
        # ...
        
        return True
    
    def stop_task(self):
        """停止当前任务"""
        if not self.running:
            return False
        
        self.running = False
        print(f"停止任务: {self.current_task.get('name')}")
        self.current_task = None
        return True
    
    def is_running(self):
        """检查是否有任务正在运行"""
        return self.running