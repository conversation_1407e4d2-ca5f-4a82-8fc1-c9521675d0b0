import os
import logging
import threading
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
import sys
import glob

logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    style="%",
    datefmt="%Y-%m-%d_%H:%M",
    level=logging.DEBUG,
    handlers=[
        logging.StreamHandler(sys.stdout),
    ])


class CustomFormatter(logging.Formatter):
    grey = '\x1b[38;21m'
    blue = '\x1b[38;5;39m'
    yellow = '\x1b[38;5;226m'
    red = '\x1b[38;5;196m'
    bold_red = '\x1b[31;1m'
    reset = '\x1b[0m'

    def __init__(self, fmt='%(asctime)s {%(name)s} [%(levelname)s] - %(message)s  Line:%(lineno)d',
                 datefmt='%Y-%m-%d %H:%M:%S', style='%'):
        super().__init__(fmt, datefmt)
        self.fmt = fmt
        self.FORMATS = {
            logging.DEBUG: self.blue + self.fmt + self.reset,
            logging.INFO: self.grey + self.fmt + self.reset,
            logging.WARNING: self.yellow + self.fmt + self.reset,
            logging.ERROR: self.red + self.fmt + self.reset,
            logging.CRITICAL: self.bold_red + self.fmt + self.reset
        }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, datefmt=self.datefmt)
        return formatter.format(record)


class MyLogger(logging.Logger):
    def exception(self, msg, *args, exc_info=True, **kwargs):
        # 设置默认开启输出堆栈信息
        super().exception(msg, *args, exc_info=exc_info, **kwargs)

    def __init__(self, name, level=logging.DEBUG, save_log=False):
        super().__init__(name, level)

        # 直接使用当前工作目录下的logs文件夹
        __log_path = os.path.join(os.getcwd(), 'logs')
        
        # 日志控制台
        console_handler = logging.StreamHandler(sys.stdout)
        self.addHandler(console_handler)
        console_handler.setLevel(level)
        max_byte = 5 * 1024 * 1024

        # 格式化显示
        formatter_console = CustomFormatter()

        formatter_file = logging.Formatter(
            fmt='%(asctime)s {%(name)s} [%(levelname)s] - %(message)s  Line:%(lineno)d',
            datefmt='%m-%d %H:%M:%S'
        )

        # 保存日志文件
        if save_log:
            if not os.path.exists(__log_path):
                os.makedirs(__log_path)

            # 清理7天前的日志文件
            self._cleanup_old_logs(__log_path)

            timestr = datetime.now().strftime("%Y-%m-%d")
            log_filename = f"{__log_path}/{timestr}.log"

            # 统一的日志文件处理器
            file_handler = RotatingFileHandler(log_filename, mode="a", encoding="utf-8", 
                                             maxBytes=max_byte, backupCount=10)
            file_handler.setLevel(logging.DEBUG)  # 记录所有级别的日志
            self.addHandler(file_handler)
            file_handler.setFormatter(formatter_file)

        # 控制台格式化显示
        console_handler.setFormatter(formatter_console)

    def _cleanup_old_logs(self, log_path):
        """清理7天前的日志文件"""
        try:
            # 计算7天前的日期
            seven_days_ago = datetime.now() - timedelta(days=7)
            
            # 查找所有日志文件
            log_files = glob.glob(os.path.join(log_path, "*.log"))
            
            for log_file in log_files:
                try:
                    # 从文件名提取日期
                    filename = os.path.basename(log_file)
                    if filename.count('-') >= 2:  # 确保是日期格式的文件名
                        date_str = filename.replace('.log', '')
                        file_date = datetime.strptime(date_str, "%Y-%m-%d")
                        
                        # 如果文件超过7天，删除它
                        if file_date < seven_days_ago:
                            os.remove(log_file)
                            print(f"已删除过期日志文件: {log_file}")
                except (ValueError, OSError) as e:
                    # 忽略无法解析日期或删除失败的文件
                    continue
        except Exception as e:
            print(f"清理日志文件时出错: {e}")


if __name__ == '__main__':
    mylogger = MyLogger('mylogger', level=logging.DEBUG, save_log=False)
    n = 20
    while n > 0:
        n -= 1
        mylogger.debug('debug')
        mylogger.info('info')
        mylogger.warning('warning')
        mylogger.error('error')
        mylogger.critical('critical')

        try:
            a, b = 1, 0
            res = a / b
        except ZeroDivisionError:
            mylogger.error('division by zero', exc_info=True)  # 保留堆栈信息
            # mylogger.exception('division by zero')  # 正常处理

    print(mylogger.parent, mylogger.getEffectiveLevel())

    # print(mylogger.handlers)








