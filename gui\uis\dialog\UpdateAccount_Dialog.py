# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'UpdateAccountDialogLhkOEF.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QDialog, QLabel, QLineEdit,
    QPushButton, QSizePolicy, QWidget)

class Ui_UpdateDialog(object):
    def setupUi(self, Dialog):
        if not Dialog.objectName():
            Dialog.setObjectName(u"Dialog")
        Dialog.resize(408, 559)
        Dialog.setStyleSheet(u"/* \u5168\u5c40\u6837\u5f0f */\n"
"QDialog {\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    color: #e0e0e0;            /* \u6d45\u7070\u8272\u6587\u5b57 */\n"
"    font-family: \"Microsoft YaHei\", \"\u5fae\u8f6f\u96c5\u9ed1\", sans-serif;\n"
"}\n"
"\n"
"#MainForm {\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}")
        self.btn_update = QPushButton(Dialog)
        self.btn_update.setObjectName(u"btn_update")
        self.btn_update.setGeometry(QRect(212, 480, 156, 40))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei"])
        font.setBold(True)
        self.btn_update.setFont(font)
        self.btn_update.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_update.setStyleSheet(u"#btn_update {\n"
"    color: #FFFFFF;\n"
"    background-color: #2B9D7C; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_update:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_back = QPushButton(Dialog)
        self.btn_back.setObjectName(u"btn_back")
        self.btn_back.setGeometry(QRect(32, 480, 156, 40))
        self.btn_back.setFont(font)
        self.btn_back.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_back.setStyleSheet(u"#btn_back {\n"
"    color: #FFFFFF;\n"
"    background-color: #21252D; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_back:hover {\n"
"        border: 1px solid #2B9D7C;\n"
"}")
        self.lineEdit_username = QLineEdit(Dialog)
        self.lineEdit_username.setObjectName(u"lineEdit_username")
        self.lineEdit_username.setGeometry(QRect(30, 60, 340, 40))
        font1 = QFont()
        font1.setPointSize(10)
        self.lineEdit_username.setFont(font1)
        self.lineEdit_username.setStyleSheet(u"#lineEdit_username {\n"
"	margin:2px;\n"
"	padding-left:8px;\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	color:#FFFFFF;\n"
"    border: 1px solid #8C8D90;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#lineEdit_username:hover {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"#lineEdit_username:focus {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"")
        self.laber_username = QLabel(Dialog)
        self.laber_username.setObjectName(u"laber_username")
        self.laber_username.setGeometry(QRect(30, 26, 91, 21))
        font2 = QFont()
        font2.setPointSize(13)
        self.laber_username.setFont(font2)
        self.laber_username.setStyleSheet(u"#laber_username {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.laber_password = QLabel(Dialog)
        self.laber_password.setObjectName(u"laber_password")
        self.laber_password.setGeometry(QRect(30, 116, 81, 21))
        self.laber_password.setFont(font2)
        self.laber_password.setStyleSheet(u"#laber_password {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.laber_name = QLabel(Dialog)
        self.laber_name.setObjectName(u"laber_name")
        self.laber_name.setGeometry(QRect(30, 292, 91, 21))
        self.laber_name.setFont(font2)
        self.laber_name.setStyleSheet(u"#laber_name {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.laber_role = QLabel(Dialog)
        self.laber_role.setObjectName(u"laber_role")
        self.laber_role.setGeometry(QRect(30, 382, 91, 21))
        self.laber_role.setFont(font2)
        self.laber_role.setStyleSheet(u"#laber_role {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.lineEdit_password = QLineEdit(Dialog)
        self.lineEdit_password.setObjectName(u"lineEdit_password")
        self.lineEdit_password.setGeometry(QRect(30, 150, 340, 40))
        self.lineEdit_password.setFont(font1)
        self.lineEdit_password.setStyleSheet(u"#lineEdit_password {\n"
"	margin:2px;\n"
"	padding-left:8px;\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	color:#FFFFFF;\n"
"    border: 1px solid #8C8D90;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#lineEdit_password:hover {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"#lineEdit_password:focus {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"")
        self.lineEdit_name = QLineEdit(Dialog)
        self.lineEdit_name.setObjectName(u"lineEdit_name")
        self.lineEdit_name.setGeometry(QRect(30, 326, 340, 40))
        self.lineEdit_name.setFont(font1)
        self.lineEdit_name.setStyleSheet(u"#lineEdit_name {\n"
"	margin:2px;\n"
"	padding-left:8px;\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	color:#FFFFFF;\n"
"    border: 1px solid #8C8D90;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#lineEdit_name:hover {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"#lineEdit_name:focus {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"")
        self.lineEdit_role = QLineEdit(Dialog)
        self.lineEdit_role.setObjectName(u"lineEdit_role")
        self.lineEdit_role.setGeometry(QRect(30, 416, 340, 40))
        self.lineEdit_role.setFont(font1)
        self.lineEdit_role.setStyleSheet(u"#lineEdit_role {\n"
"	margin:2px;\n"
"	padding-left:8px;\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	color:#FFFFFF;\n"
"    border: 1px solid #8C8D90;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#lineEdit_role:hover {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"#lineEdit_role:focus {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"")
        self.laber_pin = QLabel(Dialog)
        self.laber_pin.setObjectName(u"laber_pin")
        self.laber_pin.setGeometry(QRect(30, 202, 91, 21))
        self.laber_pin.setFont(font2)
        self.laber_pin.setStyleSheet(u"#laber_pin {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.lineEdit_pin = QLineEdit(Dialog)
        self.lineEdit_pin.setObjectName(u"lineEdit_pin")
        self.lineEdit_pin.setGeometry(QRect(30, 236, 340, 40))
        self.lineEdit_pin.setFont(font1)
        self.lineEdit_pin.setStyleSheet(u"#lineEdit_pin {\n"
"	margin:2px;\n"
"	padding-left:8px;\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	color:#FFFFFF;\n"
"    border: 1px solid #8C8D90;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#lineEdit_pin:hover {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"#lineEdit_pin:focus {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"")

        self.retranslateUi(Dialog)

        QMetaObject.connectSlotsByName(Dialog)
    # setupUi

    def retranslateUi(self, Dialog):
        Dialog.setWindowTitle(QCoreApplication.translate("Dialog", u"Dialog", None))
        self.btn_update.setText(QCoreApplication.translate("Dialog", u"保存", None))
        self.btn_back.setText(QCoreApplication.translate("Dialog", u"取消", None))
        self.lineEdit_username.setInputMask("")
        self.lineEdit_username.setPlaceholderText(QCoreApplication.translate("Dialog", u"请输入邮箱账号(必填)", None))
        self.laber_username.setText(QCoreApplication.translate("Dialog", u"📧  账号", None))
        self.laber_password.setText(QCoreApplication.translate("Dialog", u"🔑  密码", None))
        self.laber_name.setText(QCoreApplication.translate("Dialog", u"🎮  游戏名", None))
        self.laber_role.setText(QCoreApplication.translate("Dialog", u"🤖  角色", None))
        self.lineEdit_password.setInputMask("")
        self.lineEdit_password.setPlaceholderText(QCoreApplication.translate("Dialog", u"请输入密码(必填)", None))
        self.lineEdit_name.setInputMask("")
        self.lineEdit_name.setPlaceholderText(QCoreApplication.translate("Dialog", u"请输入游戏名(必填)", None))
        self.lineEdit_role.setInputMask("")
        self.lineEdit_role.setPlaceholderText(QCoreApplication.translate("Dialog", u"请输入角色", None))
        self.laber_pin.setText(QCoreApplication.translate("Dialog", u"🔑  PIN码", None))
        self.lineEdit_pin.setInputMask("")
        self.lineEdit_pin.setPlaceholderText(QCoreApplication.translate("Dialog", u"请输入PIN码(必填)", None))
    # retranslateUi

