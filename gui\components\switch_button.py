from PySide6.QtWidgets import QCheckBox
from PySide6.QtCore import QPropertyAnimation, QEasingCurve, QRect, Signal, Property
from PySide6.QtGui import QPainter, QBrush, QPen, QColor
from PySide6.QtCore import Qt

class SwitchButton(QCheckBox):
    def __init__(self, parent=None, width=44, height=22):
        super().__init__(parent)
        
        # ========== 按钮尺寸设置 ==========
        # 修改这里可以改变按钮的宽度和高度
        self.button_width = width    # 按钮宽度，默认50
        self.button_height = height  # 按钮高度，默认25
        self.setFixedSize(self.button_width, self.button_height)
        
        # ========== 滑块尺寸设置 ==========
        # 滑块大小 = 按钮高度 - 4像素边距
        self.slider_size = self.button_height - 4
        
        # ========== 重要：先初始化滑块位置 ==========
        self._slider_position = 0.0  # 确保初始化为浮点数
        
        # ========== 动画设置 ==========
        self.animation = QPropertyAnimation(self, b"sliderPosition")
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.setDuration(120)  # 减少动画时间提高流畅度
        
        # ========== 颜色配置 ==========
        # 修改这些颜色值可以改变按钮外观
        self.bg_color_off = QColor("#272B35")    # 关闭状态背景色（灰黑色）
        self.bg_color_on = QColor("#2B9D7C")     # 开启状态背景色（绿色）
        self.slider_color = QColor("#FFFFFF")    # 滑块颜色（白色）
        
        # ========== 防止多重触发 ==========
        self._is_animating = False
        
        # 连接信号
        self.toggled.connect(self.start_animation)
        self.update_slider_position()
    
    def set_colors(self, off_color="#272B35", on_color="#2B9D7C", slider_color="#FFFFFF"):
        """
        设置按钮颜色
        off_color: 关闭状态背景色
        on_color: 开启状态背景色  
        slider_color: 滑块颜色
        """
        self.bg_color_off = QColor(off_color)
        self.bg_color_on = QColor(on_color)
        self.slider_color = QColor(slider_color)
        self.update()
    
    def set_size(self, width, height):
        """
        设置按钮尺寸
        width: 按钮宽度
        height: 按钮高度
        """
        self.button_width = width
        self.button_height = height
        self.slider_size = height - 4
        self.setFixedSize(width, height)
        self.update_slider_position()
    
    def start_animation(self, checked):
        """开始动画"""
        if self._is_animating:
            return
            
        self._is_animating = True
        self.animation.stop()
        
        if checked:
            self.animation.setStartValue(self._slider_position)
            # 滑块移动到最右边 = 按钮宽度 - 滑块大小 - 边距
            self.animation.setEndValue(self.button_width - self.slider_size - 4)
        else:
            self.animation.setStartValue(self._slider_position)
            self.animation.setEndValue(0)
        
        # 动画完成后重置标志
        self.animation.finished.connect(lambda: setattr(self, '_is_animating', False))
        self.animation.start()
    
    def update_slider_position(self):
        """根据当前状态更新滑块位置"""
        if self.isChecked():
            self._slider_position = float(self.button_width - self.slider_size - 4)
        else:
            self._slider_position = 0.0
        self.update()
    
    def setChecked(self, checked):
        """重写setChecked方法"""
        super().setChecked(checked)
        self.update_slider_position()
    
    def get_slider_position(self):
        """获取滑块位置"""
        if not hasattr(self, '_slider_position'):
            self._slider_position = 0.0
        return self._slider_position
    
    def set_slider_position(self, position):
        """设置滑块位置"""
        self._slider_position = float(position)
        self.update()
    
    sliderPosition = Property(float, get_slider_position, set_slider_position)
    
    def paintEvent(self, event):
        """绘制开关按钮"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # ========== 绘制背景 ==========
        bg_color = self.bg_color_on if self.isChecked() else self.bg_color_off
        painter.setBrush(QBrush(bg_color))
        painter.setPen(QPen(Qt.PenStyle.NoPen))
        # 圆角半径 = 高度的一半，形成胶囊形状
        radius = self.button_height / 2
        painter.drawRoundedRect(0, 0, self.button_width, self.button_height, radius, radius)
        
        # ========== 绘制滑块 ==========
        painter.setBrush(QBrush(self.slider_color))
        # 滑块位置：x = 动画位置 + 边距，y = 边距
        slider_x = self._slider_position + 2
        slider_y = 2
        painter.drawEllipse(int(slider_x), slider_y, self.slider_size, self.slider_size)
    
    def mousePressEvent(self, event):
        """鼠标点击事件 - 防止多重触发"""
        if event.button() == Qt.MouseButton.LeftButton and not self._is_animating:
            # 只处理点击，不调用父类的mousePressEvent避免双重触发
            self.toggle()
        # 不调用super().mousePressEvent(event) 避免双重触发



