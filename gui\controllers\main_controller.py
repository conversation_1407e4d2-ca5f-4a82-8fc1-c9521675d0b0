import os
from PySide6.QtCore import QTimer, QDateTime, Qt, QPoint, QSize
from gui.core.json_settings import Settings
from mylogger.MyLogger import MyLogger
from utils.config_manager import config_manager
from utils.sandboxie_detector import SandboxieDetector

# 创建模块级别的日志实例
logger = MyLogger("MainController", save_log=True)

class MainController:
    """主窗口控制器，负责窗口管理、时间更新、页面控制等"""
    
    def __init__(self, ui, window):
        super().__init__()
        self.ui = ui
        self.window = window
        self.settings = Settings()
        self.drag_start_position = QPoint()
        
        # 初始化所有功能
        self.setup_connections()
        self.setup_time_timer()
        self.init_page_controllers()
        
        # 更新版本信息
        self.update_status_bar_version()
    
    # ========== 窗口拖拽功能 ==========
    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            if self.window.isMaximized():
                return
            # 只允许顶部区域拖拽
            if event.position().y() <= 44:  # 可以调整这个值来控制拖拽区域
                self.drag_start_position = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        if not self.drag_start_position.isNull():
            self.window.move(self.window.pos() + event.globalPosition().toPoint() - self.drag_start_position)
            self.drag_start_position = event.globalPosition().toPoint()
    
    def mouseReleaseEvent(self, event):
        self.drag_start_position = QPoint()
    
    # ========== 连接设置 ==========
    def setup_connections(self):
        # 连接窗口控制按钮
        self.ui.btn_close.clicked.connect(self.window.close)
        self.ui.btn_minimize.clicked.connect(self.window.showMinimized)
        self.ui.btn_maximize.clicked.connect(self.toggle_maximize)
        
        # 为按钮添加悬停效果
        self.setup_button_animations()
        
        # 连接列表和堆叠窗口
        self.ui.listWidget.currentRowChanged.connect(self.ui.stackedWidget.setCurrentIndex)
    
    # ========== 按钮动画 ==========
    def setup_button_animations(self):
        self.ui.btn_minimize.enterEvent = lambda event: self.animate_button_hover(self.ui.btn_minimize, "#FFD43B", True)
        self.ui.btn_minimize.leaveEvent = lambda event: self.animate_button_hover(self.ui.btn_minimize, "#CBAF67", False)
        
        self.ui.btn_maximize.enterEvent = lambda event: self.animate_button_hover(self.ui.btn_maximize, "#34B892", True)
        self.ui.btn_maximize.leaveEvent = lambda event: self.animate_button_hover(self.ui.btn_maximize, "#2B9D7C", False)
        
        self.ui.btn_close.enterEvent = lambda event: self.animate_button_hover(self.ui.btn_close, "#D04A57", True)
        self.ui.btn_close.leaveEvent = lambda event: self.animate_button_hover(self.ui.btn_close, "#BC4A59", False)
    
    def animate_button_hover(self, button, color, is_hover):
        button.setStyleSheet(f"#{button.objectName()} {{ background-color: {color}; border-radius: 8px; }}")
        
        if is_hover:
            new_size = QSize(int(16 * 1.10), int(16 * 1.10))
        else:
            new_size = QSize(16, 16)
        
        button.resize(new_size)
    
    def toggle_maximize(self):
        if self.window.isMaximized():
            self.window.showNormal()
        else:
            self.window.showMaximized()
    
    # ========== 时间更新功能 ==========
    def setup_time_timer(self):
        """设置时间更新定时器"""
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_system_time)
        
        interval = self.settings.items.get("time_update_interval", 1000)
        self.time_timer.start(interval)
        
        self.update_system_time()
    
    def update_system_time(self):
        """更新系统时间显示"""
        current_time = QDateTime.currentDateTime()
        
        time_format = self.settings.items.get("time_format", "yyyy/MM/dd HH:mm")
        show_seconds = self.settings.items.get("show_seconds", False)
        
        if show_seconds and "ss" not in time_format:
            time_format = time_format.replace("HH:mm", "HH:mm:ss")
        
        formatted_time = current_time.toString(time_format)
        time_text = f"系统时间：{formatted_time}"
        
        if hasattr(self.ui, 'system_time'):
            self.ui.system_time.setText(time_text)
    
    # ========== 页面控制器初始化 ==========
    def init_page_controllers(self):
        from gui.controllers.home_controller import HomeController
        from gui.controllers.account_controller import AccountController
        from gui.controllers.functions_controller import FunctionsController
        from gui.controllers.settings_controller import SettingsController
        from gui.controllers.about_controller import AboutController
        from gui.controllers.logs_controller import LogsController
        
        self.home_controller = HomeController(self.ui.page_home, self.ui)
        self.account_controller = AccountController(self.ui.page_account, self.ui, self.window)
        self.functions_controller = FunctionsController(self.ui.page_functions, self.ui)
        self.settings_controller = SettingsController(self.ui.page_settings, self.ui)
        self.about_controller = AboutController(self.ui.page_about, self.ui)
        self.logs_controller = LogsController(self.ui.page_logs, self.ui)

    def update_status_bar_version(self):
        """更新状态栏版本信息"""

        # 使用配置管理器获取设置
        settings = config_manager.get("settings")
        
        # 检测Sandboxie-Plus
        sandboxie_info = SandboxieDetector.get_sandboxie_info()
        
        if sandboxie_info['detected']:
            # 检测到Sandboxie-Plus
            sandbox_version = sandboxie_info['version']
            
            # 更新配置中的版本信息
            settings['Sandbox_version'] = f"v{sandbox_version}"
            
            # 设置绿色指示器
            if hasattr(self.ui, 'sandBoxSign'):
                self.ui.sandBoxSign.setStyleSheet("""
                    #sandBoxSign {
                        background-color: #2B9D7C;
                        border-radius: 5px;
                    }
                """)
            
            logger.info(f"Sandboxie-Plus检测成功: {sandbox_version}")

            # 保存可执行路径到settings.json
            if sandboxie_info['executable_path']:
                settings['sandboxie_executable_path'] = sandboxie_info['executable_path']
                config_manager.save_config("settings")
                logger.info(f"Sandboxie可执行路径已保存: {sandboxie_info['executable_path']}")
                
        else:
            # 未检测到Sandboxie-Plus，检查settings.json中是否已有路径
            existing_path = settings.get('sandboxie_executable_path', '')
            
            if existing_path and os.path.exists(existing_path):
                # settings.json中有有效路径，算作检测成功但版本未知
                sandbox_version = "未知"
                settings['Sandbox_version'] = f"v{sandbox_version}"
                
                # 设置绿色指示器
                if hasattr(self.ui, 'sandBoxSign'):
                    self.ui.sandBoxSign.setStyleSheet("""
                        #sandBoxSign {
                            background-color: #2B9D7C;
                            border-radius: 5px;
                        }
                    """)
                
                logger.info(f"Sandboxie-Plus检测成功: {sandbox_version}")
            else:
                # 完全未检测到Sandboxie-Plus
                sandbox_version = "未检测到"
                settings['Sandbox_version'] = "未检测到"
                
                # 设置红色指示器
                if hasattr(self.ui, 'sandBoxSign'):
                    self.ui.sandBoxSign.setStyleSheet("""
                        #sandBoxSign {
                            background-color: #F44336;
                            border-radius: 5px;
                        }
                    """)
                
                logger.warning("未检测到Sandboxie-Plus")
        
        app_version = settings.get('version', 'v1.0.0')
        app_name = settings.get('app_name', 'BetterElsrift')
        
        if hasattr(self.ui, 'label_1'):
            self.ui.label_1.setText(f"SandBox-Plus {settings['Sandbox_version']}")
        if hasattr(self.ui, 'label_2'):
            self.ui.label_2.setText(f"{app_name} {app_version}")
        
        # 检测Elsrift
        elsrift_path = settings.get('elsrift_executable_path', '')
        
        if elsrift_path and os.path.exists(elsrift_path):
            # Elsrift路径存在
            if hasattr(self.ui, 'elsriftSign'):
                self.ui.elsriftSign.setStyleSheet("""
                    #elsriftSign {
                        background-color: #2B9D7C;
                        border-radius: 5px;
                    }
                """)

        else:
            # Elsrift路径不存在
            if hasattr(self.ui, 'elsriftSign'):
                self.ui.elsriftSign.setStyleSheet("""
                    #elsriftSign {
                        background-color: #F44336;
                        border-radius: 5px;
                    }
                """)
            
            if hasattr(self.ui, 'label_3'):
                self.ui.label_3.setText("Elsrift: 未检测到")

    def cleanup(self):
        """清理资源"""
        
        # 清理日志控制器
        if hasattr(self, 'logs_controller') and self.logs_controller:
            self.logs_controller.cleanup()
        

    def __del__(self):
        """析构函数"""
        self.cleanup()












