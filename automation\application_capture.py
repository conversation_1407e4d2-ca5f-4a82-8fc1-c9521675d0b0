import cv2
import numpy as np
import mss
import pygetwindow as gw
import time

# 设置目标帧率
target_fps = 60  # 目标帧率（可以根据需要设置）
wait_time = int(1000 / target_fps)  # 毫秒

# 替换为你的游戏窗口标题
game_window_title = '[x64] Elsword - X.240901.1_1'  # 这里替换为你要捕获的游戏窗口标题

# 设置 mss 来捕获屏幕
sct = mss.mss()

while True:
    # 获取游戏窗口
    try:
        game_window = gw.getWindowsWithTitle(game_window_title)[0]
        monitor = {
            'top': game_window.top,
            'left': game_window.left,
            'width': game_window.width,
            'height': game_window.height
        }
    except IndexError:
        print("Error: Could not find game window.")
        break

    # 捕获屏幕
    img = sct.grab(monitor)
    frame = np.array(img)

    # 转换颜色通道
    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

    # 降低分辨率以提高性能（如果需要）
    frame_resized = cv2.resize(frame, (640, 480))  # 将帧调整为640x480分辨率

    # 显示捕获的帧
    cv2.imshow('Game Capture', frame_resized)

    # 使用目标帧率控制播放速度
    if cv2.waitKey(wait_time) & 0xFF == ord('q'):
        break

# 释放资源
sct.close()
cv2.destroyAllWindows()
