import os
import glob
import shutil
import pandas as pd
from sklearn.model_selection import train_test_split

# 定义数据集目录
image_dir = 'path/to/images'  # 图片目录
label_dir = 'path/to/labels'   # 标签目录

# 获取所有图片和标签文件的路径
image_paths = glob.glob(os.path.join(image_dir, '*.jpg'))  # 假设图片为.jpg格式
label_paths = [os.path.join(label_dir, os.path.basename(img).replace('.jpg', '.txt')) for img in image_paths]

# 确保图片和标签数量一致
assert len(image_paths) == len(label_paths), "图片和标签数量不匹配"

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(image_paths, label_paths, test_size=0.2, random_state=42)

# 创建训练和测试集的文件夹
os.makedirs('train/images', exist_ok=True)
os.makedirs('train/labels', exist_ok=True)
os.makedirs('test/images', exist_ok=True)
os.makedirs('test/labels', exist_ok=True)

# 移动训练集文件
for img, label in zip(X_train, y_train):
    shutil.copy(img, 'train/images/')
    shutil.copy(label, 'train/labels/')

# 移动测试集文件
for img, label in zip(X_test, y_test):
    shutil.copy(img, 'test/images/')
    shutil.copy(label, 'test/labels/')