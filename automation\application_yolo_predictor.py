import cv2
import numpy as np
from ultralytics import YOLO
import mss
import pygetwindow as gw
import time

# 加载模型
model = YOLO('runs/detect/train/weights/best.pt')

# 设置目标帧率
target_fps = 60  # 目标帧率（可以根据需要设置）
wait_time = int(1000 / target_fps)  # 毫秒

# 替换为你的游戏窗口标题
game_window_title = '[x64] Elsword - X.240901.1_1'  # 这里替换为你要捕获的游戏窗口标题

# 设置 mss 来捕获屏幕
sct = mss.mss()

while True:
    # 获取游戏窗口
    try:
        game_window = gw.getWindowsWithTitle(game_window_title)[0]
        monitor = {
            'top': game_window.top,
            'left': game_window.left,
            'width': game_window.width,
            'height': game_window.height
        }
    except IndexError:
        print("Error: Could not find game window.")
        break

    # 捕获屏幕
    img = sct.grab(monitor)
    frame = np.array(img)

    # 转换颜色通道
    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

    # 降低分辨率以提高性能
    frame_resized = cv2.resize(frame, (640, 480))  # 将帧调整为640x480分辨率

    # 进行推理
    results = model.predict(source=frame_resized, conf=0.25)  # 将置信度阈值设为0.25

    # 处理结果
    for result in results:
        boxes = result.boxes  # 获取检测框
        for box in boxes:
            # 在帧上绘制检测框
            x1, y1, x2, y2 = box.xyxy[0]  # 框的坐标
            confidence = box.conf[0]  # 置信度
            cls = int(box.cls[0])  # 类别，确保为整数

            # 绘制矩形框
            cv2.rectangle(frame_resized, (int(x1), int(y1)), (int(x2), int(y2)), (255, 0, 0), 2)
            cv2.putText(frame_resized, f'Class: {cls}, Conf: {confidence:.2f}',
                        (int(x1), int(y1) - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

    # 显示带有检测框的帧
    cv2.imshow('Game Detection', frame_resized)

    # 使用目标帧率控制播放速度
    if cv2.waitKey(wait_time) & 0xFF == ord('q'):
        break

# 释放资源
sct.close()
cv2.destroyAllWindows()
