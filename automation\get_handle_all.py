import mss
import numpy as np
import cv2
import win32gui
import pygetwindow as gw
import psutil

# 列出所有进程的名称和进程 ID
for proc in psutil.process_iter(['pid', 'name']):
    print(proc.info)

# 根据进程 ID 获取窗口句柄
def get_window_by_pid(pid):
    windows = gw.getWindowsWithTitle('')
    for window in windows:
        if window._pid == pid:
            return window._hWnd
    raise Exception(f"No window found for PID: {pid}")

# 替换为你的进程 ID
process_id = 6080  # 这里输入你的进程 ID
try:
    hwnd = get_window_by_pid(process_id)
    print(f"窗口句柄: {hwnd}")
except Exception as e:
    print(e)
    exit()

# 获取窗口的位置信息
def get_window_rect(hwnd):
    rect = win32gui.GetWindowRect(hwnd)
    return {
        'top': rect[1],
        'left': rect[0],
        'width': rect[2] - rect[0],
        'height': rect[3] - rect[1]
    }

monitor = get_window_rect(hwnd)
print(f"窗口位置信息: {monitor}")

# 创建屏幕捕获对象
sct = mss.mss()

while True:
    # 捕获游戏窗口的屏幕图像
    img = sct.grab(monitor)

    # 将图像转换为 OpenCV 格式
    frame = np.array(img)
    frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

    # 显示捕获的图像
    cv2.imshow('Game Capture', frame)

    # 按 'q' 键退出
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# 释放资源
cv2.destroyAllWindows()
