from PySide6.QtCore import QObject, QTimer, QRect, Qt ,QSize
from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
                               QPushButton, QSizePolicy, QFileDialog, QMessageBox, QDialog)
from PySide6.QtGui import QFont, QCursor
from services.account_service import AccountService, AccountStatus
from datetime import datetime
from gui.uis.dialog.ImportAccount_Dialog import Ui_ImportDialog
from gui.uis.dialog.UpdateAccount_Dialog import Ui_UpdateDialog
from gui.components.switch_button import SwitchButton
from gui.components.toast_manager import show_toast
from mylogger.MyLogger import MyLogger

# 创建模块级别的日志实例
logger = MyLogger("AccountController", save_log=True)

class AccountController(QObject):
    def __init__(self, page_widget, ui, window):
        super().__init__()
        self.page = page_widget
        self.ui = ui
        self.window = window  # 保存父窗口引用
        self.account_widgets = {}  # 存储动态创建的widget
        
        # 初始化账户服务
        self.account_service = AccountService()
        
        # 定时器更新状态
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_account_display)
        self.timer.start(1000)
        
        self.setup_connections()
        self.load_accounts()
    
    def setup_connections(self):
        """绑定按钮信号槽"""
        self.ui.btn_refreshState.clicked.connect(self.refresh_accounts)
        self.ui.btn_addAccount.clicked.connect(self.show_import_dialog)  # 添加账户按钮
        self.ui.btn_export.clicked.connect(self.export_accounts)  # 导出账户按钮

    def load_accounts(self):
        """加载并显示账户列表"""
        accounts_data = self.account_service.load_accounts()
        status_data = self.account_service.load_status()
        
        accounts = accounts_data.get("accounts", [])
        
        # 更新头部信息
        self.update_header_info()
        
        self.update_account_list(accounts, status_data)
    
    def create_account_item(self, account, status):
        """动态创建与UI文件完全一致的账户项"""
        account_id = str(account['id'])
        
        # 主容器 - accountItem
        accountItem = QWidget()
        accountItem.setObjectName(f"accountItem_{account_id}")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy.setHeightForWidth(accountItem.sizePolicy().hasHeightForWidth())
        accountItem.setSizePolicy(sizePolicy)
        accountItem.setMinimumSize(QSize(0, 86))
        
        # 主水平布局
        horizontalLayout_7 = QHBoxLayout(accountItem)
        horizontalLayout_7.setSpacing(0)
        horizontalLayout_7.setObjectName(f"horizontalLayout_7_{account_id}")
        horizontalLayout_7.setContentsMargins(0, 0, 0, 0)

        # 左侧区域 - Item_left (基本信息)
        Item_left = QWidget(accountItem)
        Item_left.setObjectName(f"Item_left_{account_id}")
        Item_left.setMinimumSize(QSize(222, 0))
        
        # 账户名标签
        label_account = QLabel(Item_left)
        label_account.setObjectName(f"label_account_{account_id}")
        label_account.setGeometry(QRect(0, -2, 221, 16))
        font1 = QFont()
        font1.setFamilies(["Microsoft YaHei"])
        font1.setBold(True)
        label_account.setFont(font1)
        label_account.setStyleSheet(f"""#label_account_{account_id} {{
            color:#2B9D7C;
            background-color: transparent; 
            font-size: 14px;
        }}""")
        label_account.setText(account["username"])
        
        # 密码标签
        label_password = QLabel(Item_left)
        label_password.setObjectName(f"label_password_{account_id}")
        label_password.setGeometry(QRect(0, 22, 31, 20))
        label_password.setStyleSheet("color: #9CA2AE;")
        label_password.setText("密码：")
        
        # 密码值
        label_passwordText = QLabel(Item_left)
        label_passwordText.setObjectName(f"label_passwordText_{account_id}")
        label_passwordText.setGeometry(QRect(38, 22, 91, 20))
        label_passwordText.setFont(font1)
        label_passwordText.setStyleSheet(f"""#label_passwordText_{account_id} {{
            color:#FFFFFF;
        }}""")
        label_passwordText.setText(account["password"])
        
        # PIN标签
        label_pin = QLabel(Item_left)
        label_pin.setObjectName(f"label_pin_{account_id}")
        label_pin.setGeometry(QRect(0, 52, 31, 16))
        label_pin.setStyleSheet("color: #9CA2AE;")
        label_pin.setText("PIN：")
        
        # PIN值
        label_pinText = QLabel(Item_left)
        label_pinText.setObjectName(f"label_pinText_{account_id}")
        label_pinText.setGeometry(QRect(32, 50, 50, 20))
        label_pinText.setFont(font1)
        label_pinText.setText(account["pin_code"])
        
        # 角色标签
        label_role = QLabel(Item_left)
        label_role.setObjectName(f"label_role_{account_id}")
        label_role.setGeometry(QRect(84, 52, 32, 16))
        label_role.setStyleSheet("color: #9CA2AE;")
        label_role.setText("角色：")
        
        # 角色值
        label_roleText = QLabel(Item_left)
        label_roleText.setObjectName(f"label_roleText_{account_id}")
        label_roleText.setGeometry(QRect(120, 50, 82, 20))
        label_roleText.setFont(font1)
        role_display = f"{account['role_id']} ({account['role']})"
        label_roleText.setText(role_display)
        
        horizontalLayout_7.addWidget(Item_left)
        
        # 中间区域 - Item_middle (实时数据)
        Item_middle = QWidget(accountItem)
        Item_middle.setObjectName(f"Item_middle_{account_id}")
        Item_middle.setMinimumSize(QSize(170, 0))
        
        # 最后登录标签
        label_lastlogin = QLabel(Item_middle)
        label_lastlogin.setObjectName(f"label_lastlogin_{account_id}")
        label_lastlogin.setGeometry(QRect(0, -2, 51, 16))
        label_lastlogin.setStyleSheet("color: #9CA2AE;")
        label_lastlogin.setText("最后登录：")
        
        # 最后登录值
        label_lastloginText = QLabel(Item_middle)
        label_lastloginText.setObjectName(f"label_lastloginText_{account_id}")
        label_lastloginText.setGeometry(QRect(60, -4, 112, 20))
        label_lastloginText.setFont(font1)
        last_login = status.get("last_login", "未登录")
        if last_login and last_login != "未登录":
            try:
                from datetime import datetime
                dt = datetime.strptime(last_login, "%Y-%m-%d %H:%M:%S")
                last_login = dt.strftime("%Y/%m/%d %H:%M")
            except:
                pass
        label_lastloginText.setText(last_login)
        
        # 最后退出标签
        label_lastout = QLabel(Item_middle)
        label_lastout.setObjectName(f"label_lastout_{account_id}")
        label_lastout.setGeometry(QRect(0, 24, 61, 16))
        label_lastout.setStyleSheet("color: #9CA2AE;")
        label_lastout.setText("最后退出:")
        
        # 最后退出值
        label_lastoutText = QLabel(Item_middle)
        label_lastoutText.setObjectName(f"label_lastoutText_{account_id}")
        label_lastoutText.setGeometry(QRect(70, 22, 112, 20))
        label_lastoutText.setFont(font1)
        last_logout = status.get("last_logout", "未退出")
        if last_logout and last_logout != "未退出":
            try:
                from datetime import datetime
                dt = datetime.strptime(last_logout, "%Y-%m-%d %H:%M:%S")
                last_logout = dt.strftime("%Y/%m/%d %H:%M")
            except:
                pass
        label_lastoutText.setText(last_logout)
        
        # 运行时间标签
        label_runtime = QLabel(Item_middle)
        label_runtime.setObjectName(f"label_runtime_{account_id}")
        label_runtime.setGeometry(QRect(0, 52, 51, 16))
        label_runtime.setStyleSheet("color: #9CA2AE;")
        label_runtime.setText("运行时间:")
        
        # 运行时间值
        label_runtimeText = QLabel(Item_middle)
        label_runtimeText.setObjectName(f"label_runtimeText_{account_id}")
        label_runtimeText.setGeometry(QRect(60, 50, 50, 20))
        label_runtimeText.setFont(font1)
        runtime_hours = status.get("current_runtime", 0) / 3600
        label_runtimeText.setText(f"{runtime_hours:.1f}h")
        
        horizontalLayout_7.addWidget(Item_middle)
        
        # 状态区域 - Item_status
        Item_status = QWidget(accountItem)
        Item_status.setObjectName(f"Item_status_{account_id}")
        Item_status.setMinimumSize(QSize(170, 0))

        # 状态标签 - 使用与ui_main.py一致的字体大小
        label_status = QLabel(Item_status)
        label_status.setObjectName(f"label_state_{account_id}")
        label_status.setGeometry(QRect(0, 30, 100, 20))
        # 与ui_main.py一致的字体设置：14pt + Bold
        font5 = QFont()
        font5.setFamilies(["Microsoft YaHei"])
        font5.setPointSize(14)
        font5.setBold(True)
        label_status.setFont(font5)

        # 首先检查账户是否启用
        is_enabled = status.get("enabled", True)
        if not is_enabled:
            # 账户被禁用，显示"已禁用"状态
            label_status.setText("已禁用")
            label_status.setStyleSheet("color: #F44336;")  # 使用灰色
        else:
            # 账户启用，根据运行状态显示不同状态
            account_status = status.get("status", "waiting")
            if account_status == "running":
                label_status.setText("运行中")
                label_status.setStyleSheet("color: #4CAF50;")
            elif account_status == "cooldown":
                label_status.setText("冷却中")
                label_status.setStyleSheet("color: #2196F3;")
            elif account_status == "error":
                label_status.setText("错误")
                label_status.setStyleSheet("color: #F44336;")
            else:
                label_status.setText("等待中")
                label_status.setStyleSheet("color: #9CA2AE;")

        horizontalLayout_7.addWidget(Item_status)

        # 操作区域 - Item_right
        Item_right = QWidget(accountItem)
        Item_right.setObjectName(f"Item_right_{account_id}")
        Item_right.setMinimumSize(QSize(120, 0))

        # ========== 创建自定义开关按钮 ==========
        switch_toggle = SwitchButton(Item_right, width=44, height=22)
        switch_toggle.setObjectName(f"switch_toggle_{account_id}")
        switch_toggle.setGeometry(QRect(2, 25, 44, 22))
        
        # ========== 设置自定义颜色 ==========
        switch_toggle.set_colors(
            off_color="#272B35",
            on_color="#2B9D7C",
            slider_color="#FFFFFF"
        )
        
        # 修改：从status中获取enabled状态，而不是从account中
        is_enabled = status.get("enabled", True)  # 从status获取enabled状态
        switch_toggle.setChecked(is_enabled)
        switch_toggle.toggled.connect(lambda checked, aid=account["id"]: self.toggle_account(aid, checked))

        # 修改按钮
        btn_modify = QPushButton(Item_right)
        btn_modify.setObjectName(f"btn_modify_{account_id}")
        btn_modify.setGeometry(QRect(58, 2, 60, 26))
        btn_modify.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        btn_modify.setStyleSheet(f"""#btn_modify_{account_id} {{
            background-color:#3A3E47;
            border-radius: 10px;
            font-size: 12px;
            color: white;
        }}
        #btn_modify_{account_id}:hover {{
            background-color: #21252D;
        }}""")
        btn_modify.setText("修改")
        btn_modify.clicked.connect(lambda: self.modify_account(account["id"]))

        # 删除按钮
        btn_delete = QPushButton(Item_right)
        btn_delete.setObjectName(f"btn_delete_{account_id}")
        btn_delete.setGeometry(QRect(58, 32, 60, 26))
        btn_delete.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        btn_delete.setStyleSheet(f"""#btn_delete_{account_id} {{
            background-color:#D32F2F;
            border-radius: 10px;
            font-size: 12px;
            color: white;
        }}
        #btn_delete_{account_id}:hover {{
            background-color: #EF5350;
        }}""")
        btn_delete.setText("删除")
        btn_delete.clicked.connect(lambda: self.delete_account(account["id"]))
        
        horizontalLayout_7.addWidget(Item_right)
        
        # 设置布局比例
        horizontalLayout_7.setStretch(0, 2)
        horizontalLayout_7.setStretch(1, 2)
        horizontalLayout_7.setStretch(2, 2)
        horizontalLayout_7.setStretch(3, 1)
        
        return accountItem
    
    def get_bold_font(self):
        """获取粗体字体"""
        font = QFont()
        font.setFamilies(["Microsoft YaHei"])
        font.setBold(True)
        return font
    
    def update_account_list(self, accounts, status_data):
        """更新账户列表显示"""
        # 清除现有的账户项
        self.clear_account_items()
        
        # 获取滚动区域的布局
        layout = self.ui.verticalLayout_12
        
        # 为每个账户创建项
        for account in accounts:
            account_id = str(account["id"])
            status = status_data.get(account_id, {})
            
            # 创建账户项
            account_item = self.create_account_item(account, status)
            
            # 添加到布局
            layout.addWidget(account_item, 0, Qt.AlignmentFlag.AlignTop)
            
            # 保存引用
            self.account_widgets[account_id] = account_item
        
        # 添加弹性空间确保顶部对齐
        layout.addStretch()
    
    def clear_account_items(self):
        """清除所有账户项"""
        layout = self.ui.verticalLayout_12
        
        # 移除所有动态创建的widget
        for account_id, widget in self.account_widgets.items():
            layout.removeWidget(widget)
            widget.deleteLater()
        
        # TODO: 注释代码用于查看账号Item样式
        # 清除布局中的所有项目（包括stretch）   
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.spacerItem():
                # 移除弹性空间
                pass
        
        self.account_widgets.clear()
    
    def delete_account(self, account_id):
        """删除账户"""
        # 先获取账户信息用于日志显示
        account = self.account_service.get_account_by_id(account_id)
        if not account:
            show_toast("账户不存在", "error")
            return
        
        reply = QMessageBox.question(
            None, 
            "确认删除", 
            f"确定要删除账户 {account['username']} ({account['role_id']}) 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success = self.account_service.delete_account(account_id)
            if success:
                logger.info(f"账户 {account['username']} ({account['role_id']}) 删除成功")
                show_toast("账户删除成功", "success")
                
                # 更新多开配置
                self._update_multi_launch_config_after_account_change(account)
                
                self.load_accounts()
            else:
                logger.error(f"删除账户 {account['username']} ({account['role_id']}) 失败")
                show_toast("删除失败,删除账户时发生错误!", "error")

    def refresh_accounts(self):
        """刷新账户状态"""
        print("刷新账户状态")
        self.load_accounts()
        show_toast("账户状态刷新成功", "success")
    
    def import_accounts(self, dialog=None, overlay=None):
        """导入账户"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.window,
            "选择账户文件",
            "",
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if file_path:
            logger.info(f"开始导入账户文件: {file_path}")
            result = self.account_service.import_accounts_from_file(file_path)
            
            if result is not None:
                # 记录详细的导入信息
                imported_accounts = result.get('imported_accounts', [])
                for account in imported_accounts:
                    logger.info(f"成功导入账户: {account['username']} ({account['role_id']})")
                
                message = f"成功导入 {result['imported_count']} 个账户，跳过 {result['skipped_count']} 个重复账户"
                logger.info(message)
                show_toast(message, "success")
                self.load_accounts()
                # 导入完成后关闭对话框
                if dialog and overlay:
                    overlay.deleteLater()
                    dialog.accept()
            else:
                logger.error(f"导入账户文件失败: {file_path}")
                show_toast("文件格式错误", "error")
                # 导入失败后也关闭对话框
                if dialog and overlay:
                    overlay.deleteLater()
                    dialog.reject()
        # 如果用户取消选择文件，不关闭对话框

    def export_accounts(self):
        """导出账户"""
        file_path, _ = QFileDialog.getSaveFileName(
            None,
            "保存账户文件",
            "accounts_export.json",
            "JSON文件 (*.json)"
        )
        
        if file_path:
            success = self.account_service.export_accounts_to_file(file_path, include_status=False)
            if success:
                show_toast(f"导出成功,账户已导出到: {file_path}", "success")
                # QMessageBox.information(None, "导出成功", f"账户已导出到: {file_path}")
    
    def update_account_display(self):
        """定时更新账户显示（运行时间等）"""
        # TODO: 更新运行时间显示
        pass
    
    def start_account(self, account_id):
        """启动指定账户"""
        account = self.account_service.get_account_by_id(account_id)
        if account:
            logger.info(f"启动账户 {account['username']} ({account['role_id']})")
            self.account_service.update_account_status(
                account_id, 
                AccountStatus.RUNNING,
                work_start_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
            self.update_header_info()

    def stop_account(self, account_id):
        """停止指定账户"""
        account = self.account_service.get_account_by_id(account_id)
        if account:
            logger.info(f"停止账户 {account['username']} ({account['role_id']})")
            self.account_service.update_account_status(
                account_id,
                AccountStatus.WAITING,
                last_logout=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
            self.update_header_info()  # 只更新头部信息，不重新加载整个列表
    
    def modify_account(self, account_id):
        """修改账户"""
        # 获取账户信息
        account = self.account_service.get_account_by_id(account_id)
        if not account:
            show_toast("账户不存在", "error")
            return
        
        dialog, dialog_ui, overlay = self._create_modal_dialog(Ui_UpdateDialog)
        
        # 填充现有账户信息
        self._fill_account_form(dialog_ui, account)
        
        # 绑定按钮事件
        dialog_ui.btn_update.clicked.connect(lambda: self.update_account(account_id, dialog, dialog_ui, overlay))
        dialog_ui.btn_back.clicked.connect(lambda: self.close_dialog(dialog, overlay))
        
        dialog.exec()
    
    def _fill_account_form(self, dialog_ui, account):
        """填充账户表单"""
        dialog_ui.lineEdit_username.setText(account.get('username', ''))
        dialog_ui.lineEdit_password.setText(account.get('password', ''))
        dialog_ui.lineEdit_pin.setText(account.get('pin_code', ''))
        dialog_ui.lineEdit_name.setText(account.get('role', ''))
        dialog_ui.lineEdit_role.setText(account.get('role_id', ''))

    def update_account(self, account_id, dialog, dialog_ui, overlay):
        """更新账户信息"""
        # 先获取原账户信息用于日志
        old_account = self.account_service.get_account_by_id(account_id)
        if not old_account:
            show_toast("账户不存在", "error")
            return
        
        username = dialog_ui.lineEdit_username.text().strip()
        password = dialog_ui.lineEdit_password.text().strip()
        pin_code = dialog_ui.lineEdit_pin.text().strip()
        role = dialog_ui.lineEdit_name.text().strip()
        role_id = dialog_ui.lineEdit_role.text().strip()
        
        # 验证输入
        if not self._validate_account_input(username, password, pin_code):
            return
        
        # 检查重复（排除当前账户）
        if self._check_account_duplicate(username, role_id, exclude_id=account_id):
            show_toast(f"账户已存在: {username} - {role_id}", "error")
            return
        
        # 更新账户
        updates = {
            'username': username,
            'password': password,
            'pin_code': pin_code,
            'role': role,
            'role_id': role_id
        }
        
        success = self.account_service.update_account(account_id, updates)
        if success:
            logger.info(f"账户 {old_account['username']} ({old_account['role_id']}) 已修改")
            show_toast("账户信息更新成功", "success")
            overlay.close()
            dialog.close()
            self.load_accounts()
        else:
            logger.error(f"修改账户 {old_account['username']} ({old_account['role_id']}) 失败")
            show_toast("更新失败", "error")

    def show_import_dialog(self):
        """显示导入账户对话框"""
        dialog, dialog_ui, overlay = self._create_modal_dialog(Ui_ImportDialog)
        
        # 绑定对话框按钮事件
        dialog_ui.btn_Import.clicked.connect(lambda: self.import_accounts(dialog, overlay))
        dialog_ui.btn_add.clicked.connect(lambda: self.add_single_account(dialog, dialog_ui, overlay))
        dialog_ui.btn_back.clicked.connect(lambda: self.close_dialog(dialog, overlay))
        
        dialog.exec()

    def close_dialog(self, dialog, overlay):
        """关闭对话框并移除遮罩层"""
        overlay.deleteLater()
        dialog.reject()

    def add_single_account(self, dialog, dialog_ui, overlay):
        """从对话框添加单个账户"""
        username = dialog_ui.lineEdit_username.text().strip()
        password = dialog_ui.lineEdit_password.text().strip()
        pin_code = dialog_ui.lineEdit_pin.text().strip()
        character = dialog_ui.lineEdit_name.text().strip()
        role = dialog_ui.lineEdit_role.text().strip()
        
        # 验证输入
        if not self._validate_account_input(username, password, pin_code):
            return
        
        # 检查重复
        if self._check_account_duplicate(username, role):
            show_toast(f"账户已存在: {username} - {role}", "error")
            return
        
        # 添加账户
        account = self.account_service.add_account(username, password, pin_code, character, role)
        if account:
            app_logger.info(f"新增账户 {username} ({role}) 成功")
            show_toast("账户添加成功", "success")
            self._clear_form(dialog_ui)
            self.load_accounts()
        else:
            app_logger.error(f"新增账户 {username} ({role}) 失败")
            show_toast("添加账户失败", "error")

    def toggle_account(self, account_id, checked):
        """切换账户启用/禁用状态"""
        try:
            account = self.account_service.get_account_by_id(account_id)
            if not account:
                show_toast("账户不存在", "error")
                return
            
            # 更新账户启用状态
            success = self.account_service.update_account(account_id, {"enabled": checked})
            if success:
                status_text = "启用" if checked else "禁用"
                logger.info(f"账户 {account['username']} ({account['role_id']}) 已{status_text}")
                show_toast(f"账户已{status_text}", "success")
                
                # 如果是禁用账户，更新多开配置
                if not checked:
                    self._update_multi_launch_config_after_account_change(account)
                
                # 实时更新UI显示
                self.update_account_item_status(account_id, checked)
                
                # 更新头部统计信息
                self.update_header_info()
            else:
                logger.error(f"切换账户 {account['username']} ({account['role_id']}) 状态失败")
                show_toast("切换状态失败", "error")
        except Exception as e:
            logger.error(f"切换账户状态时出错: {e}")
            show_toast("操作失败", "error")
    
    def update_account_item_status(self, account_id, is_enabled):
        """实时更新单个账户项的状态显示"""
        account_widget = self.account_widgets.get(str(account_id))
        if not account_widget:
            return
        
        # 查找状态标签
        status_label = account_widget.findChild(QLabel, f"label_state_{account_id}")
        if status_label:
            if not is_enabled:
                # 账户被禁用，显示"已禁用"状态
                status_label.setText("已禁用")
                status_label.setStyleSheet("color: #9CA2AE;")
            else:
                # 账户启用，需要获取当前运行状态
                status_data = self.account_service.load_status()
                current_status = status_data.get(str(account_id), {}).get("status", "waiting")
                
                if current_status == "running":
                    status_label.setText("运行中")
                    status_label.setStyleSheet("color: #4CAF50;")
                elif current_status == "cooldown":
                    status_label.setText("冷却中")
                    status_label.setStyleSheet("color: #2196F3;")
                elif current_status == "error":
                    status_label.setText("错误")
                    status_label.setStyleSheet("color: #F44336;")
                else:
                    status_label.setText("等待中")
                    status_label.setStyleSheet("color: #9CA2AE;")
    
    def update_header_info(self):
        """更新头部统计信息"""
        accounts_data = self.account_service.load_accounts()
        status_data = self.account_service.load_status()
        
        accounts = accounts_data.get("accounts", [])
        total_count = len(accounts)
        enabled_count = sum(1 for account in accounts 
                          if status_data.get(str(account["id"]), {}).get("enabled", True))
        
        # 只统计启用账户的运行状态
        running_count = sum(1 for acc_id, status in status_data.items() 
                          if status.get("enabled", True) and status.get("status") == "running")
        cooldown_count = sum(1 for acc_id, status in status_data.items() 
                           if status.get("enabled", True) and status.get("status") == "cooldown")
        
        self.ui.head_info.setText(
            f"共 {total_count} 个账户（{enabled_count} 个启用，{running_count} 个运行中，{cooldown_count} 个冷却中）"
        )

    def _validate_account_input(self, username, password, pin_code):
        """验证账户输入"""
        if not username or not password or not pin_code:
            show_toast("账号、密码和PIN码不能为空", "error")
            return False
        return True
    
    def _check_account_duplicate(self, username, role_id, exclude_id=None):
        """检查账户是否重复"""
        accounts_data = self.account_service.load_accounts()
        existing_accounts = accounts_data.get('accounts', [])
        
        for existing_account in existing_accounts:
            # 如果有排除ID，跳过该账户
            if exclude_id and existing_account.get('id') == exclude_id:
                continue
                
            if (existing_account.get('username') == username and 
                existing_account.get('role_id') == role_id):
                return True
        return False

    def _create_modal_dialog(self, dialog_ui_class):
        """创建模态对话框的通用方法"""
        # 创建遮罩层
        overlay = QWidget(self.window)
        overlay.setStyleSheet("background-color: rgba(0, 0, 0, 0.6);")
        overlay.resize(self.window.size())
        overlay.show()
        
        dialog = QDialog(self.window)
        dialog_ui = dialog_ui_class()
        dialog_ui.setupUi(dialog)
        
        # 设置窗口标志：无边框、无系统菜单
        dialog.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        dialog.setModal(True)
        
        # 居中显示
        self._center_dialog(dialog)
        
        return dialog, dialog_ui, overlay
    
    def _center_dialog(self, dialog):
        """居中显示对话框"""
        parent_geometry = self.window.geometry()
        dialog_geometry = dialog.geometry()
        x = parent_geometry.x() + (parent_geometry.width() - dialog_geometry.width()) // 2
        y = parent_geometry.y() + (parent_geometry.height() - dialog_geometry.height()) // 2
        dialog.move(x, y)

    def _clear_form(self, dialog_ui):
        """清空表单"""
        dialog_ui.lineEdit_username.clear()
        dialog_ui.lineEdit_password.clear()
        dialog_ui.lineEdit_pin.clear()
        dialog_ui.lineEdit_name.clear()
        dialog_ui.lineEdit_role.clear()

    def _update_multi_launch_config_after_account_change(self, account):
        """账户删除或禁用后更新多开配置"""
        try:
            from utils.config_manager import config_manager
            
            # 构造账户标识
            account_identifier = f"{account['username']}({account['role']})"
            
            # 获取当前多开配置
            multi_config = config_manager.get("multi_launch", default={})
            launch_config = multi_config.get("multi_launch", {})
            selected_accounts = launch_config.get("selected_accounts", [])
            
            # 如果选中的账户中包含被删除/禁用的账户
            if account_identifier in selected_accounts:
                # 移除该账户
                selected_accounts.remove(account_identifier)
                
                # 更新数量
                new_count = max(1, len(selected_accounts))
                
                # 更新配置
                config_data = {
                    "multi_launch": {
                        "count": new_count,
                        "selected_accounts": selected_accounts,
                        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    },
                    "settings": {
                        "auto_restore_selection": True,
                        "max_concurrent": 5
                    }
                }
                
                config_manager.update_and_save("multi_launch", config_data)
                
                # 通知首页重新加载（如果首页控制器可访问）
                self._notify_home_controller_reload()
                
        except Exception as e:
            logger.error(f"更新多开配置失败: {e}")

    def _notify_home_controller_reload(self):
        """通知首页控制器重新加载多开数据"""
        try:
            # 通过父窗口获取主控制器，然后访问首页控制器
            if hasattr(self.window, 'main_controller'):
                main_controller = self.window.main_controller
                if hasattr(main_controller, 'home_controller'):
                    home_controller = main_controller.home_controller
                    # 重新加载多开配置
                    selected_accounts = home_controller.load_multi_launch_config()
                    # 更新账号选择区域
                    current_count = home_controller.get_current_count()
                    home_controller.update_account_selection(current_count)
                    # 恢复账号选择
                    home_controller.restore_account_selections(selected_accounts)
                    logger.info("首页多开数据已重新加载")
        except Exception as e:
            show_toast("通知首页重新加载失败", "error")