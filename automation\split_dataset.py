import os
import random
import shutil

# 设置路径
train_images_path = 'F:/Program_code/EIAUTO/datasets/train/images'
train_labels_path = 'F:/Program_code/EIAUTO/datasets/train/labels'
val_images_path = 'F:/Program_code/EIAUTO/datasets/val/images'
val_labels_path = 'F:/Program_code/EIAUTO/datasets/val/labels'

# 创建验证集目录
os.makedirs(val_images_path, exist_ok=True)
os.makedirs(val_labels_path, exist_ok=True)

# 获取训练集图像文件列表
image_files = [f for f in os.listdir(train_images_path) if f.endswith(('.jpg', '.png'))]
random.shuffle(image_files)  # 随机打乱顺序

# 设置验证集大小
val_size = int(len(image_files) * 0.2)  # 20% 的数据用于验证集
val_images = image_files[:val_size]  # 选择前 val_size 张作为验证集

# 复制文件到验证集目录
for image in val_images:
    shutil.copy(os.path.join(train_images_path, image), val_images_path)
    label_file = image.replace('.jpg', '.txt').replace('.png', '.txt')  # 假设标签文件以 .txt 存在
    shutil.copy(os.path.join(train_labels_path, label_file), val_labels_path)

print(f"已将 {val_size} 张图像复制到验证集")
split_dataset.py