from PySide6.QtCore import QObject, QTimer, QThread, Signal
from PySide6.QtWidgets import QFileDialog
from PySide6.QtGui import QTextCharFormat, QColor, QTextCursor
from PySide6.QtGui import QDesktopServices
from PySide6.QtCore import QUrl
import os
import subprocess
from datetime import datetime
from pathlib import Path
import re
from mylogger.MyLogger import MyLogger

class LogReader(QThread):
    """日志读取线程"""
    log_updated = Signal(str)
    error_occurred = Signal(str)
    
    def __init__(self, log_file_path, start_position=0):
        super().__init__()
        self.log_file_path = log_file_path
        self.last_position = start_position
        self.running = True
        self._stop_requested = False
    
    def run(self):
        """监控日志文件变化"""
        while self.running and not self._stop_requested:
            try:
                if os.path.exists(self.log_file_path):
                    with open(self.log_file_path, 'r', encoding='utf-8') as f:
                        f.seek(self.last_position)
                        new_content = f.read()
                        if new_content and not self._stop_requested:
                            self.log_updated.emit(new_content)
                            self.last_position = f.tell()
                
                # 使用事件循环而不是sleep，更好的响应停止请求
                if not self._stop_requested:
                    self.msleep(1000)
                    
            except Exception as e:
                self.error_occurred.emit(f"读取日志文件错误: {e}")
                if not self._stop_requested:
                    self.msleep(5000)
    
    def stop(self):
        """安全停止线程"""
        self._stop_requested = True
        self.running = False
        self.quit()  # 退出事件循环

class LogsController(QObject):
    def __init__(self, page_widget, ui):
        super().__init__()
        self.logger = MyLogger("LogsController", save_log=True)
        self.page = page_widget
        self.ui = ui
        self.logs_dir = Path("logs")
        self.current_filters = set()
        self.log_reader = None
        self.cached_logs = []
        self.last_file_size = 0
        
        # 统一的日志级别配置 - 与MyLogger3保持一致
        self.log_level_config = {
            'DEBUG': {'color': QColor(56, 123, 255), 'hex': '#3875FF'},
            'INFO': {'color': QColor(158, 158, 158), 'hex': '#9E9E9E'},
            'WARNING': {'color': QColor(255, 226, 77), 'hex': '#FFE24D'},
            'ERROR': {'color': QColor(255, 107, 107), 'hex': '#FF6B6B'},
            'CRITICAL': {'color': QColor(255, 71, 87), 'hex': '#FF4757'}
        }
        
        self.setup_connections()
        self.load_today_logs()

    def setup_connections(self):
        """设置信号槽连接"""
        # 使用字典简化按钮连接
        filter_buttons = {
            'INFO': self.ui.btn_info,
            'WARNING': self.ui.btn_warning,
            'ERROR': self.ui.btn_error,
            'DEBUG': self.ui.btn_debug,
            'CRITICAL': self.ui.btn_critical
        }
        
        for level, button in filter_buttons.items():
            button.clicked.connect(lambda checked, l=level: self.toggle_filter(l))
        
        # 功能按钮
        self.ui.btn_refreshLogs.clicked.connect(self.refresh_logs)
        self.ui.btn_pathLogs.clicked.connect(self.open_logs_directory)
    
    def toggle_filter(self, level):
        """切换日志级别过滤"""
        if level in self.current_filters:
            self.current_filters.remove(level)
        else:
            self.current_filters.add(level)
        
        # 更新按钮样式
        self.update_filter_button_style(level)
        
        # 只重新显示，不重新读取文件
        self.display_filtered_logs()
    
    def update_filter_button_style(self, level):
        """更新过滤按钮样式"""
        button_map = {
            'INFO': self.ui.btn_info,
            'WARNING': self.ui.btn_warning,
            'ERROR': self.ui.btn_error,
            'DEBUG': self.ui.btn_debug,
            'CRITICAL': self.ui.btn_critical
        }
        
        # 按钮对应的边框颜色 - 与MyLogger3颜色一致
        border_colors = {
            'INFO': '#9E9E9E',
            'WARNING': '#FFE24D',
            'ERROR': '#FF6B6B',
            'DEBUG': '#3875FF',
            'CRITICAL': '#FF4757'
        }
        
        button = button_map.get(level)
        border_color = border_colors.get(level)
        
        if button and border_color:
            if level in self.current_filters:
                # 激活状态
                button.setStyleSheet(f"""
                    #{button.objectName()} {{
                        background-color: {border_color};
                        color: #FFFFFF;
                        border: 1px solid {border_color};
                        border-radius: 10px;
                    }}
                """)
            else:
                # 默认状态
                button.setStyleSheet(f"""
                    #{button.objectName()} {{
                        color: {border_color};
                        background-color: #1B1E24;
                        border: 1px solid {border_color};
                        border-radius: 10px;
                    }}
                    #{button.objectName()}:hover {{
                        background-color: rgba({self._hex_to_rgba(border_color)}, 0.2);
                    }}
                """)

    def _hex_to_rgba(self, hex_color):
        """将十六进制颜色转换为RGB值"""
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i+2], 16)) for i in (0, 2, 4))

    def load_today_logs(self):
        """加载今日日志"""
        today = datetime.now().strftime("%Y-%m-%d")
        log_file = self.logs_dir / f"{today}.log"
        
        if log_file.exists():
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                # 解析并缓存所有日志行
                self.parse_and_cache_logs(content)
                self.display_filtered_logs()
                
                # 记录当前文件大小，用于监控时跳过已读取的内容
                self.last_file_size = log_file.stat().st_size
                
                # 重新启动监控
                self.start_log_monitoring()
            except Exception as e:
                self.ui.logs_text.setPlainText(f"读取日志文件失败: {e}")
        else:
            self.cached_logs = []
            self.last_file_size = 0
            self.ui.logs_text.setPlainText("今日暂无日志记录")
            self.start_log_monitoring()

    def parse_and_cache_logs(self, content):
        """解析并缓存日志内容"""
        try:
            self.cached_logs = []
            lines = content.strip().split('\n')
            
            for line in lines:
                if not line.strip():
                    continue
                
                level = self.extract_log_level(line)
                self.cached_logs.append({
                    'line': line,
                    'level': level
                })
            
            self.logger.debug(f"成功解析 {len(self.cached_logs)} 条日志记录")
        except Exception as e:
            self.logger.error(f"解析日志内容失败: {e}")
            self.cached_logs = []

    def display_filtered_logs(self):
        """显示过滤后的日志"""
        self.ui.logs_text.clear()
        cursor = self.ui.logs_text.textCursor()
        
        displayed_lines = 0
        
        for log_item in self.cached_logs:
            line = log_item['line']
            level = log_item['level']
            
            # 应用过滤器
            if self.current_filters and level not in self.current_filters:
                continue
            
            self.insert_colored_log_line(cursor, line, level)
            displayed_lines += 1
        
        # 如果有过滤器但没有匹配的日志，显示提示信息
        if self.current_filters and displayed_lines == 0:
            self.show_no_matching_logs()
        
        # 滚动到底部
        self.ui.logs_text.moveCursor(QTextCursor.End)

    def insert_colored_log_line(self, cursor, line, level):
        """插入带颜色的日志行 - 适配MyLogger3格式"""
        # 匹配MyLogger3的时间格式 (MM-DD HH:MM:SS)
        time_match = re.match(r'^(\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
        
        if time_match:
            time_part = time_match.group(1)
            rest_part = line[len(time_part):]
            
            # 时间部分 - 固定灰色
            time_format = QTextCharFormat()
            time_format.setForeground(QColor('#9E9E9E'))
            cursor.insertText(time_part, time_format)
            
            # 其余部分 - 根据日志级别着色
            content_format = QTextCharFormat()
            level_color = self.log_level_config.get(level, {}).get('color', QColor('#9E9E9E'))
            content_format.setForeground(level_color)
            cursor.insertText(rest_part + '\n', content_format)
        else:
            # 整行使用默认颜色
            format = QTextCharFormat()
            format.setForeground(QColor('#9E9E9E'))
            cursor.insertText(line + '\n', format)
    
    def extract_log_level(self, line):
        """从日志行中提取日志级别 - 适配MyLogger3格式"""
        # MyLogger3格式: MM-DD HH:MM:SS - name - LEVEL - message Line:XX
        match = re.search(r' - (DEBUG|INFO|WARNING|ERROR|CRITICAL) - ', line)
        return match.group(1) if match else None
    
    def start_log_monitoring(self):
        """启动日志监控"""
        # 停止之前的监控
        if hasattr(self, 'log_reader') and self.log_reader and self.log_reader.isRunning():
            self.log_reader.log_updated.disconnect()
            self.log_reader.stop()
            self.log_reader.wait()
        
        today = datetime.now().strftime("%Y-%m-%d")
        log_file = self.logs_dir / f"{today}.log"
        
        # 传递当前文件大小，让LogReader从这个位置开始读取
        start_position = getattr(self, 'last_file_size', 0)
        self.log_reader = LogReader(str(log_file), start_position)
        self.log_reader.log_updated.connect(self.on_new_log_content)
        self.log_reader.start()

    def on_new_log_content(self, new_content):
        """处理新的日志内容"""
        # 直接追加显示新日志
        self.append_new_logs_direct(new_content)

    def append_new_logs_direct(self, new_content):
        """直接追加新的日志内容"""
        lines = new_content.strip().split('\n')
        
        # 检查当前是否显示"没有匹配的日志记录"
        current_text = self.ui.logs_text.toPlainText()
        is_showing_no_match = "没有匹配的日志记录" in current_text
        
        cursor = self.ui.logs_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        displayed_lines = 0
        
        for line in lines:
            if not line.strip():
                continue
            
            level = self.extract_log_level(line)
            
            # 添加到缓存
            self.cached_logs.append({
                'line': line,
                'level': level
            })
            
            # 应用过滤器
            if self.current_filters and level not in self.current_filters:
                continue
            
            # 如果之前显示"没有匹配的日志记录"，先清空
            if is_showing_no_match and displayed_lines == 0:
                self.ui.logs_text.clear()
                cursor = self.ui.logs_text.textCursor()
                is_showing_no_match = False
            
            self.insert_colored_log_line(cursor, line, level)
            displayed_lines += 1
        
        # 滚动到底部
        self.ui.logs_text.moveCursor(QTextCursor.End)

    def show_no_matching_logs(self):
        """显示没有匹配的日志记录提示"""
        cursor = self.ui.logs_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # 设置提示文字格式
        format = QTextCharFormat()
        format.setForeground(QColor('#9E9E9E'))  # 灰色
        
        # 显示提示信息
        cursor.insertText("没有匹配的日志记录\n", format)
    
    def refresh_logs(self):
        """刷新日志"""
        self.load_today_logs()
        self.logger.info("日志已刷新")
    
    def open_logs_directory(self):
        """打开日志目录 - 使用QDesktopServices"""
        logs_path = os.path.abspath(self.logs_dir)
        if os.path.exists(logs_path):
            # 使用QDesktopServices异步打开目录
            url = QUrl.fromLocalFile(logs_path)
            success = QDesktopServices.openUrl(url)
            
            if success:
                self.logger.info("日志目录已打开")
            else:
                self.logger.error("打开日志目录失败")
        else:
            self.logger.error(f"日志目录不存在: {logs_path}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'log_reader') and self.log_reader:
                if self.log_reader.isRunning():
                    self.log_reader.log_updated.disconnect()
                    self.log_reader.stop()
                    if not self.log_reader.wait(3000):  # 等待最多3秒
                        self.log_reader.terminate()  # 强制终止
                        self.log_reader.wait(1000)  # 再等1秒确保终止
                self.log_reader = None
            self.logger.debug("日志控制器资源清理完成")
        except Exception as e:
            self.logger.error(f"清理日志控制器资源时出错: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup()



















