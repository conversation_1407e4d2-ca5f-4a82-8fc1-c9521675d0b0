# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'MainWindowsiAwdsT.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QFrame, QHBoxLayout,
    QLabel, QLineEdit, QListWidget, QListWidgetItem,
    QPlainTextEdit, QPushButton, QScrollArea, QSizePolicy,
    QSpacerItem, QStackedWidget, QVBoxLayout, QWidget)

class Ui_MainForm(object):
    def setupUi(self, MainForm):
        if not MainForm.objectName():
            MainForm.setObjectName(u"MainForm")
        MainForm.setWindowModality(Qt.WindowModality.NonModal)
        MainForm.setEnabled(True)
        MainForm.resize(1100, 780)
        MainForm.setMinimumSize(QSize(1100, 780))
        MainForm.setStyleSheet(u"/* \u5168\u5c40\u6837\u5f0f */\n"
"QWidget {\n"
"    background-color: #15161B; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    color: #e0e0e0;            /* \u6d45\u7070\u8272\u6587\u5b57 */\n"
"    font-family: \"Microsoft YaHei\", \"\u5fae\u8f6f\u96c5\u9ed1\", sans-serif;\n"
"}\n"
"\n"
"#MainForm {\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}")
        self.verticalLayout_8 = QVBoxLayout(MainForm)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.headFrame = QWidget(MainForm)
        self.headFrame.setObjectName(u"headFrame")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.headFrame.sizePolicy().hasHeightForWidth())
        self.headFrame.setSizePolicy(sizePolicy)
        self.headFrame.setMinimumSize(QSize(0, 38))
        self.verticalLayout_13 = QVBoxLayout(self.headFrame)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.verticalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.headFrame_right = QFrame(self.headFrame)
        self.headFrame_right.setObjectName(u"headFrame_right")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.headFrame_right.sizePolicy().hasHeightForWidth())
        self.headFrame_right.setSizePolicy(sizePolicy1)
        self.headFrame_right.setMinimumSize(QSize(100, 30))
        self.headFrame_right.setFrameShape(QFrame.Shape.StyledPanel)
        self.headFrame_right.setFrameShadow(QFrame.Shadow.Raised)
        self.btn_close = QPushButton(self.headFrame_right)
        self.btn_close.setObjectName(u"btn_close")
        self.btn_close.setGeometry(QRect(80, 2, 16, 16))
        self.btn_close.setMinimumSize(QSize(0, 16))
        self.btn_close.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_close.setStyleSheet(u"#btn_close  {\n"
"    background-color: #BC4A59;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}")
        self.btn_maximize = QPushButton(self.headFrame_right)
        self.btn_maximize.setObjectName(u"btn_maximize")
        self.btn_maximize.setGeometry(QRect(50, 2, 16, 16))
        self.btn_maximize.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_maximize.setStyleSheet(u"#btn_maximize  {\n"
"    background-color: #2B9D7C;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}")
        self.btn_minimize = QPushButton(self.headFrame_right)
        self.btn_minimize.setObjectName(u"btn_minimize")
        self.btn_minimize.setEnabled(True)
        self.btn_minimize.setGeometry(QRect(20, 2, 16, 16))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei"])
        font.setPointSize(16)
        font.setBold(False)
        self.btn_minimize.setFont(font)
        self.btn_minimize.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_minimize.setStyleSheet(u"#btn_minimize {\n"
"    background-color: #CBAF67;\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"")
        self.btn_minimize.setIconSize(QSize(16, 16))

        self.verticalLayout_13.addWidget(self.headFrame_right, 0, Qt.AlignmentFlag.AlignRight)


        self.verticalLayout_8.addWidget(self.headFrame)

        self.InnerWidget = QWidget(MainForm)
        self.InnerWidget.setObjectName(u"InnerWidget")
        self.InnerWidget.setStyleSheet(u"")
        self.horizontalLayout = QHBoxLayout(self.InnerWidget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(-1, 0, -1, 9)
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(-1, 0, -1, -1)
        self.LeftFarme = QWidget(self.InnerWidget)
        self.LeftFarme.setObjectName(u"LeftFarme")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.LeftFarme.sizePolicy().hasHeightForWidth())
        self.LeftFarme.setSizePolicy(sizePolicy2)
        self.LeftFarme.setMinimumSize(QSize(0, 0))
        self.LeftFarme.setMaximumSize(QSize(16777215, 16777215))
        self.LeftFarme.setStyleSheet(u"#LeftFarme {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    color: #e0e0e0;            /* \u6d45\u7070\u8272\u6587\u5b57 */\n"
"    margin:0px 20px 0 0;\n"
"    border: 1px solid #20242A; /* \u8fb9\u6846\u5206\u9694\u7ebf */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}\n"
"")
        self.verticalLayout_6 = QVBoxLayout(self.LeftFarme)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.appTitle = QLabel(self.LeftFarme)
        self.appTitle.setObjectName(u"appTitle")
        font1 = QFont()
        font1.setFamilies([u"Microsoft YaHei"])
        font1.setBold(True)
        self.appTitle.setFont(font1)
        self.appTitle.setStyleSheet(u"#appTitle {\n"
"    color: #2A9B7A;\n"
"    background-color: transparent; \n"
"    font-size: 24px;\n"
"    font-weight: bold;\n"
"    margin: 20px 0 10px 8px;\n"
"    text-align: center;\n"
"}")

        self.verticalLayout_6.addWidget(self.appTitle)

        self.listWidget = QListWidget(self.LeftFarme)
        QListWidgetItem(self.listWidget)
        QListWidgetItem(self.listWidget)
        QListWidgetItem(self.listWidget)
        QListWidgetItem(self.listWidget)
        QListWidgetItem(self.listWidget)
        QListWidgetItem(self.listWidget)
        self.listWidget.setObjectName(u"listWidget")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.listWidget.sizePolicy().hasHeightForWidth())
        self.listWidget.setSizePolicy(sizePolicy3)
        self.listWidget.setMinimumSize(QSize(0, 0))
        font2 = QFont()
        font2.setFamilies([u"Microsoft YaHei"])
        font2.setBold(False)
        self.listWidget.setFont(font2)
        self.listWidget.viewport().setProperty(u"cursor", QCursor(Qt.CursorShape.PointingHandCursor))
        self.listWidget.setToolTipDuration(-1)
        self.listWidget.setStyleSheet(u"/* \u5217\u8868\u63a7\u4ef6\u6837\u5f0f */\n"
"#listWidget {\n"
"    background-color: transparent; /* \u900f\u660e\u80cc\u666f */\n"
"    border: none; /* \u65e0\u8fb9\u6846 */\n"
"    margin:0 20px 0 0;\n"
"    font-size: 16px;\n"
"    color: #cbd5e0; /* \u6587\u5b57\u989c\u8272 */\n"
"    outline: 0; /* \u79fb\u9664\u7126\u70b9\u6846 */\n"
"    show-decoration-selected: 0; /* \u79fb\u9664\u9009\u4e2d\u88c5\u9970 */\n"
"}\n"
"\n"
"/* \u5217\u8868\u9879\u6837\u5f0f */\n"
"#listWidget::item {\n"
"    height: 50px; /* \u9879\u9ad8\u5ea6 */\n"
"    margin: 5px 10px; /* \u4e0a\u4e0b\u95f4\u8ddd5px\uff0c\u5de6\u53f3\u95f4\u8ddd10px */\n"
"    padding: 0px 15px; /* \u5185\u8fb9\u8ddd */\n"
"    background-color: transparent; /* \u900f\u660e\u80cc\u666f */\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"/* \u5217\u8868\u9879\u60ac\u505c\u6548\u679c */\n"
"#listWidget::item:hover {\n"
"    color: white; /* \u9009\u4e2d\u6587\u5b57\u989c\u8272 */\n"
"    background-color: #2d3748; /* \u60ac\u505c\u80cc\u666f"
                        "\u8272 */\n"
"}\n"
"\n"
"/* \u5217\u8868\u9879\u9009\u4e2d\u6548\u679c */\n"
"#listWidget::item:selected {\n"
"    background-color: #2B9D7C; /* \u9009\u4e2d\u80cc\u666f\u8272 */\n"
"    color: white; /* \u9009\u4e2d\u6587\u5b57\u989c\u8272 */\n"
"    font-weight: bold; /* \u52a0\u7c97 */\n"
"}\n"
"")
        self.listWidget.setSortingEnabled(False)

        self.verticalLayout_6.addWidget(self.listWidget)

        self.versionText = QWidget(self.LeftFarme)
        self.versionText.setObjectName(u"versionText")
        self.versionText.setMinimumSize(QSize(0, 100))
        self.versionText.setStyleSheet(u"#versionText {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    margin: 0 20px 10px 8px;\n"
"}")
        self.label_1 = QLabel(self.versionText)
        self.label_1.setObjectName(u"label_1")
        self.label_1.setGeometry(QRect(58, 37, 142, 16))
        self.label_1.setStyleSheet(u"#label_1 {\n"
"    color: #9CA2AE;\n"
"    background-color: transparent;\n"
"}")
        self.sandBoxSign = QPushButton(self.versionText)
        self.sandBoxSign.setObjectName(u"sandBoxSign")
        self.sandBoxSign.setGeometry(QRect(40, 40, 10, 10))
        font3 = QFont()
        font3.setFamilies([u"Microsoft YaHei"])
        font3.setPointSize(16)
        self.sandBoxSign.setFont(font3)
        self.sandBoxSign.setStyleSheet(u"#sandBoxSign {\n"
"    background-color: #2B9D7C;\n"
"    border-radius: 5px; /* \u5706\u89d2 */\n"
"}\n"
"")
        self.label_2 = QLabel(self.versionText)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setGeometry(QRect(70, 80, 120, 16))
        self.label_2.setStyleSheet(u"#label_2 {\n"
"    color: #9CA2AE;\n"
"    background-color: transparent;\n"
"}")
        self.system_time = QLabel(self.versionText)
        self.system_time.setObjectName(u"system_time")
        self.system_time.setGeometry(QRect(30, 10, 161, 16))
        font4 = QFont()
        font4.setFamilies([u"Microsoft YaHei"])
        self.system_time.setFont(font4)
        self.system_time.setStyleSheet(u"#system_time {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"    font-size: 12px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}")
        self.elsriftSign = QPushButton(self.versionText)
        self.elsriftSign.setObjectName(u"elsriftSign")
        self.elsriftSign.setGeometry(QRect(40, 60, 10, 10))
        self.elsriftSign.setFont(font3)
        self.elsriftSign.setStyleSheet(u"#elsriftSign {\n"
"    background-color: #2B9D7C;\n"
"    border-radius: 5px; /* \u5706\u89d2 */\n"
"}\n"
"")
        self.label_3 = QLabel(self.versionText)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setGeometry(QRect(58, 57, 142, 16))
        self.label_3.setStyleSheet(u"#label_3 {\n"
"    color: #9CA2AE;\n"
"    background-color: transparent;\n"
"}")

        self.verticalLayout_6.addWidget(self.versionText)


        self.horizontalLayout_5.addWidget(self.LeftFarme)

        self.stackedWidget = QStackedWidget(self.InnerWidget)
        self.stackedWidget.setObjectName(u"stackedWidget")
        self.stackedWidget.setFont(font2)
        self.stackedWidget.setStyleSheet(u"")
        self.page_home = QWidget()
        self.page_home.setObjectName(u"page_home")
        self.verticalLayout = QVBoxLayout(self.page_home)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.scrollArea_home = QScrollArea(self.page_home)
        self.scrollArea_home.setObjectName(u"scrollArea_home")
        self.scrollArea_home.setWidgetResizable(True)
        self.sah_Layout = QWidget()
        self.sah_Layout.setObjectName(u"sah_Layout")
        self.sah_Layout.setGeometry(QRect(0, 0, 780, 705))
        self.verticalLayout_9 = QVBoxLayout(self.sah_Layout)
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.verticalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.home_head = QWidget(self.sah_Layout)
        self.home_head.setObjectName(u"home_head")
        sizePolicy.setHeightForWidth(self.home_head.sizePolicy().hasHeightForWidth())
        self.home_head.setSizePolicy(sizePolicy)
        self.home_head.setMinimumSize(QSize(0, 200))
        self.home_head.setStyleSheet(u"#home_head {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border: 1px solid #20242A; /* \u8fb9\u6846\u5206\u9694\u7ebf */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}")
        self.btn_run = QPushButton(self.home_head)
        self.btn_run.setObjectName(u"btn_run")
        self.btn_run.setGeometry(QRect(280, 70, 181, 61))
        font5 = QFont()
        font5.setFamilies([u"Microsoft YaHei"])
        font5.setPointSize(20)
        self.btn_run.setFont(font5)
        self.label_acc_1 = QLabel(self.home_head)
        self.label_acc_1.setObjectName(u"label_acc_1")
        self.label_acc_1.setGeometry(QRect(10, 150, 61, 31))
        font6 = QFont()
        font6.setFamilies([u"Microsoft YaHei"])
        font6.setPointSize(13)
        self.label_acc_1.setFont(font6)
        self.label_acc_1.setStyleSheet(u"#label_acc_1 {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.comboBox_1 = QComboBox(self.home_head)
        self.comboBox_1.addItem("")
        self.comboBox_1.addItem("")
        self.comboBox_1.addItem("")
        self.comboBox_1.addItem("")
        self.comboBox_1.addItem("")
        self.comboBox_1.setObjectName(u"comboBox_1")
        self.comboBox_1.setGeometry(QRect(80, 150, 261, 31))
        self.comboBox_1.setStyleSheet(u"#comboBox_1 {\n"
"    background-color: #282C36; \n"
"	padding-left:10px;\n"
"}\n"
"\n"
"    /* \u4e0b\u62c9\u5217\u8868\u6837\u5f0f */\n"
"#comboBox_1 QAbstractItemView {\n"
"        background-color: #202429; /* \u4e0b\u62c9\u5217\u8868\u80cc\u666f\u8272 */\n"
"        border: 1px solid #292C33;\n"
"        border-radius: 4px;    /* \u4e0b\u62c9\u5217\u8868\u5706\u89d2 */\n"
"		padding-left:10px;\n"
"    }\n"
"\n"
"#comboBox_1 QAbstractItemView::item {\n"
"            height: 30px;  /* \u9879\u7684\u9ad8\u5ea6 */\n"
"            margin-bottom: 2px;  /* \u9879\u4e4b\u95f4\u7684\u95f4\u8ddd */\n"
"    }")
        self.btn_del = QPushButton(self.home_head)
        self.btn_del.setObjectName(u"btn_del")
        self.btn_del.setGeometry(QRect(360, 150, 60, 26))
        self.btn_del.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_del.setStyleSheet(u"#btn_del {\n"
"	background-color:#D32F2F;\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 12px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_del:hover {\n"
"    background-color: #EF5350; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_initialize_sandbox = QPushButton(self.home_head)
        self.btn_initialize_sandbox.setObjectName(u"btn_initialize_sandbox")
        self.btn_initialize_sandbox.setGeometry(QRect(560, 70, 101, 61))

        self.verticalLayout_9.addWidget(self.home_head)

        self.home_account = QWidget(self.sah_Layout)
        self.home_account.setObjectName(u"home_account")
        self.home_account.setStyleSheet(u"#home_account {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border: 1px solid #20242A; /* \u8fb9\u6846\u5206\u9694\u7ebf */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"	margin-top:10px;\n"
"}")
        self.verticalLayout_14 = QVBoxLayout(self.home_account)
        self.verticalLayout_14.setObjectName(u"verticalLayout_14")
        self.verticalLayout_14.setContentsMargins(9, 9, 9, 9)
        self.home_account_1 = QWidget(self.home_account)
        self.home_account_1.setObjectName(u"home_account_1")
        sizePolicy.setHeightForWidth(self.home_account_1.sizePolicy().hasHeightForWidth())
        self.home_account_1.setSizePolicy(sizePolicy)
        self.home_account_1.setMinimumSize(QSize(0, 60))
        self.home_account_1.setStyleSheet(u"#home_account_1 {\n"
"    background-color: transparent; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"   	border-top: none;\n"
"    border-right: none;\n"
"    border-bottom: 2px solid #292C33;\n"
"    border-left: none;\n"
"	margin:0 10px 0 10px;\n"
"}")
        self.label_nums = QLabel(self.home_account_1)
        self.label_nums.setObjectName(u"label_nums")
        self.label_nums.setGeometry(QRect(10, 14, 131, 31))
        font7 = QFont()
        font7.setFamilies([u"Microsoft YaHei"])
        font7.setPointSize(14)
        font7.setBold(True)
        self.label_nums.setFont(font7)
        self.label_nums.setStyleSheet(u"#label_nums {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.btn_add = QPushButton(self.home_account_1)
        self.btn_add.setObjectName(u"btn_add")
        self.btn_add.setGeometry(QRect(210, 14, 31, 31))
        self.btn_add.setFont(font1)
        self.btn_add.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_add.setStyleSheet(u"#btn_add {\n"
"    color: #FFFFFF;\n"
"    background-color: #21252D; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 20px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_add:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_minus = QPushButton(self.home_account_1)
        self.btn_minus.setObjectName(u"btn_minus")
        self.btn_minus.setGeometry(QRect(250, 14, 31, 31))
        self.btn_minus.setFont(font1)
        self.btn_minus.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_minus.setStyleSheet(u"#btn_minus {\n"
"    color: #FFFFFF;\n"
"    background-color: #21252D; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 20px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_minus:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.lineEdit_count = QLineEdit(self.home_account_1)
        self.lineEdit_count.setObjectName(u"lineEdit_count")
        self.lineEdit_count.setGeometry(QRect(140, 14, 51, 31))
        font8 = QFont()
        font8.setFamilies([u"Microsoft YaHei"])
        font8.setPointSize(11)
        self.lineEdit_count.setFont(font8)
        self.lineEdit_count.setStyleSheet(u"#lineEdit_count {\n"
"	margin:2px;\n"
"    background-color: #121317; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	color:#FFFFFF;\n"
"    border: 1px solid #8C8D90;\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#lineEdit_count:hover {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"#lineEdit_count:focus {\n"
"    border: 1px solid #2B9D7C;\n"
"}\n"
"\n"
"")
        self.lineEdit_count.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.lineEdit_count.setReadOnly(False)

        self.verticalLayout_14.addWidget(self.home_account_1)


        self.verticalLayout_9.addWidget(self.home_account)

        self.scrollArea_home.setWidget(self.sah_Layout)

        self.verticalLayout.addWidget(self.scrollArea_home)

        self.stackedWidget.addWidget(self.page_home)
        self.page_account = QWidget()
        self.page_account.setObjectName(u"page_account")
        self.page_account.setStyleSheet(u"")
        self.verticalLayout_10 = QVBoxLayout(self.page_account)
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.verticalLayout_10.setContentsMargins(-1, 4, -1, -1)
        self.account_head = QWidget(self.page_account)
        self.account_head.setObjectName(u"account_head")
        sizePolicy.setHeightForWidth(self.account_head.sizePolicy().hasHeightForWidth())
        self.account_head.setSizePolicy(sizePolicy)
        self.account_head.setMinimumSize(QSize(0, 62))
        self.account_head.setStyleSheet(u"#account_head {\n"
"   	border-top: none;\n"
"    border-right: none;\n"
"    border-bottom: 3px solid #2D323A;\n"
"    border-left: none;\n"
"}\n"
"")
        self.horizontalLayout_4 = QHBoxLayout(self.account_head)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 9, -1, -1)
        self.account_head_left = QWidget(self.account_head)
        self.account_head_left.setObjectName(u"account_head_left")
        sizePolicy2.setHeightForWidth(self.account_head_left.sizePolicy().hasHeightForWidth())
        self.account_head_left.setSizePolicy(sizePolicy2)
        self.account_head_left.setMinimumSize(QSize(320, 0))
        self.head_info = QLabel(self.account_head_left)
        self.head_info.setObjectName(u"head_info")
        self.head_info.setGeometry(QRect(0, 0, 341, 20))
        self.head_info.setFont(font4)
        self.head_info.setStyleSheet(u"#head_info {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"    font-size: 13.5px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}")

        self.horizontalLayout_4.addWidget(self.account_head_left)

        self.horizontalSpacer_3 = QSpacerItem(199, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_3)

        self.account_head_right = QWidget(self.account_head)
        self.account_head_right.setObjectName(u"account_head_right")
        sizePolicy2.setHeightForWidth(self.account_head_right.sizePolicy().hasHeightForWidth())
        self.account_head_right.setSizePolicy(sizePolicy2)
        self.account_head_right.setMinimumSize(QSize(320, 0))
        self.btn_refreshState = QPushButton(self.account_head_right)
        self.btn_refreshState.setObjectName(u"btn_refreshState")
        self.btn_refreshState.setGeometry(QRect(226, 2, 94, 34))
        self.btn_refreshState.setFont(font1)
        self.btn_refreshState.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_refreshState.setStyleSheet(u"#btn_refreshState {\n"
"    color: #FFFFFF;\n"
"    background-color: #2B9D7C; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_refreshState:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_addAccount = QPushButton(self.account_head_right)
        self.btn_addAccount.setObjectName(u"btn_addAccount")
        self.btn_addAccount.setGeometry(QRect(120, 2, 94, 34))
        self.btn_addAccount.setFont(font1)
        self.btn_addAccount.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_addAccount.setStyleSheet(u"#btn_addAccount {\n"
"    color: #FFFFFF;\n"
"    background-color: #21252D; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_addAccount:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_export = QPushButton(self.account_head_right)
        self.btn_export.setObjectName(u"btn_export")
        self.btn_export.setGeometry(QRect(10, 2, 94, 34))
        self.btn_export.setFont(font1)
        self.btn_export.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_export.setStyleSheet(u"#btn_export {\n"
"    color: #FFFFFF;\n"
"    background-color: #21252D; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_export:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")

        self.horizontalLayout_4.addWidget(self.account_head_right)


        self.verticalLayout_10.addWidget(self.account_head)

        self.account_body = QFrame(self.page_account)
        self.account_body.setObjectName(u"account_body")
        sizePolicy.setHeightForWidth(self.account_body.sizePolicy().hasHeightForWidth())
        self.account_body.setSizePolicy(sizePolicy)
        self.account_body.setMinimumSize(QSize(0, 60))
        self.account_body.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
        self.account_body.setStyleSheet(u"#account_body {\n"
"    margin-top:10px;\n"
"    background-color: #21252D; /* \u7070\u80cc\u666f */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}\n"
"")
        self.account_body.setFrameShape(QFrame.Shape.StyledPanel)
        self.account_body.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_6 = QHBoxLayout(self.account_body)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.title_account = QLabel(self.account_body)
        self.title_account.setObjectName(u"title_account")
        self.title_account.setFont(font1)
        self.title_account.setStyleSheet(u"#title_account {\n"
"	color:#FFFFFF;\n"
"	background-color: transparent; \n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"	margin-left:20px;\n"
"}")

        self.horizontalLayout_6.addWidget(self.title_account)

        self.title_realdata = QLabel(self.account_body)
        self.title_realdata.setObjectName(u"title_realdata")
        self.title_realdata.setFont(font1)
        self.title_realdata.setStyleSheet(u"#title_realdata {\n"
"	color:#FFFFFF;\n"
"	background-color: transparent; \n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}")

        self.horizontalLayout_6.addWidget(self.title_realdata)

        self.title_state = QLabel(self.account_body)
        self.title_state.setObjectName(u"title_state")
        self.title_state.setFont(font1)
        self.title_state.setStyleSheet(u"#title_state {\n"
"	color:#FFFFFF;\n"
"	background-color: transparent; \n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}")

        self.horizontalLayout_6.addWidget(self.title_state)

        self.title_operate = QLabel(self.account_body)
        self.title_operate.setObjectName(u"title_operate")
        self.title_operate.setFont(font1)
        self.title_operate.setStyleSheet(u"#title_operate {\n"
"	color:#FFFFFF;\n"
"	background-color: transparent; \n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"	margin-right:20px;\n"
"}")

        self.horizontalLayout_6.addWidget(self.title_operate)

        self.horizontalLayout_6.setStretch(0, 2)
        self.horizontalLayout_6.setStretch(1, 2)
        self.horizontalLayout_6.setStretch(2, 2)
        self.horizontalLayout_6.setStretch(3, 1)

        self.verticalLayout_10.addWidget(self.account_body)

        self.scrollArea_account = QScrollArea(self.page_account)
        self.scrollArea_account.setObjectName(u"scrollArea_account")
        self.scrollArea_account.setStyleSheet(u"/* ===== \u6eda\u52a8\u6761\u6837\u5f0f ===== */\n"
"/* \u5782\u76f4\u6eda\u52a8\u6761 */\n"
"#scrollArea_account QScrollBar:vertical {\n"
"    border: none;               /* \u65e0\u8fb9\u6846 */\n"
"    background-color: #2A2E36; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    width: 10px;                /* \u6eda\u52a8\u6761\u5bbd\u5ea6 */\n"
"    border-radius: 4px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u624b\u67c4 */\n"
"#scrollArea_account QScrollBar::handle:vertical {\n"
"    background: #4A4D56;        /* \u624b\u67c4\u989c\u8272 */\n"
"    min-height: 20px;            /* \u6700\u5c0f\u9ad8\u5ea6 */\n"
"    border-radius: 5px;          /* \u5706\u89d2 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u624b\u67c4\u60ac\u505c\u6548\u679c */\n"
"#scrollArea_account QScrollBar::handle:vertical:hover {\n"
"    background: #5A5D66;        /* \u60ac\u505c\u989c\u8272 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u624b\u67c4\u6309\u4e0b\u6548\u679c */\n"
"#scrollArea_account QScrollBar::handle:vertical:pressed "
                        "{\n"
"    background: #5A5D66;        /* \u6309\u4e0b\u989c\u8272 */\n"
"}\n"
"\n"
"\n"
"/* \u6eda\u52a8\u6761\u6309\u94ae\uff08\u4e0a\u4e0b\u7bad\u5934\uff09 */\n"
"#scrollArea_account QScrollBar::add-line:vertical, \n"
"#scrollArea_account QScrollBar::sub-line:vertical,\n"
"#scrollArea_account QScrollBar::add-line:horizontal, \n"
"#scrollArea_account QScrollBar::sub-line:horizontal {\n"
"	background: none;        /* \u6309\u4e0b\u989c\u8272 */\n"
"}\n"
"\n"
"/* \u9690\u85cf\u6eda\u52a8\u6761\u69fd */\n"
"#scrollArea_account QScrollBar::add-page:vertical, \n"
"#scrollArea_account QScrollBar::sub-page:vertical,\n"
"#scrollArea_account QScrollBar::add-page:horizontal, \n"
"#scrollArea_account QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}")
        self.scrollArea_account.setWidgetResizable(True)
        self.saa_Layout = QWidget()
        self.saa_Layout.setObjectName(u"saa_Layout")
        self.saa_Layout.setEnabled(True)
        self.saa_Layout.setGeometry(QRect(0, 0, 570, 104))
        self.verticalLayout_12 = QVBoxLayout(self.saa_Layout)
        self.verticalLayout_12.setObjectName(u"verticalLayout_12")
        self.accountItem = QWidget(self.saa_Layout)
        self.accountItem.setObjectName(u"accountItem")
        sizePolicy.setHeightForWidth(self.accountItem.sizePolicy().hasHeightForWidth())
        self.accountItem.setSizePolicy(sizePolicy)
        self.accountItem.setMinimumSize(QSize(0, 86))
        self.horizontalLayout_7 = QHBoxLayout(self.accountItem)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.Item_left = QWidget(self.accountItem)
        self.Item_left.setObjectName(u"Item_left")
        sizePolicy2.setHeightForWidth(self.Item_left.sizePolicy().hasHeightForWidth())
        self.Item_left.setSizePolicy(sizePolicy2)
        self.Item_left.setMinimumSize(QSize(222, 0))
        self.label_account = QLabel(self.Item_left)
        self.label_account.setObjectName(u"label_account")
        self.label_account.setGeometry(QRect(0, -2, 221, 16))
        self.label_account.setFont(font1)
        self.label_account.setStyleSheet(u"#label_account {\n"
"	color:#2B9D7C;\n"
"	background-color: transparent; \n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}")
        self.label_passwordText = QLabel(self.Item_left)
        self.label_passwordText.setObjectName(u"label_passwordText")
        self.label_passwordText.setGeometry(QRect(38, 28, 91, 20))
        self.label_passwordText.setFont(font1)
        self.label_passwordText.setStyleSheet(u"#password {\n"
"	color:#FFFFFF;\n"
"	background-color: transparent; \n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"	margin-left:20px;\n"
"}")
        self.label_role = QLabel(self.Item_left)
        self.label_role.setObjectName(u"label_role")
        self.label_role.setGeometry(QRect(84, 60, 31, 16))
        self.label_role.setStyleSheet(u"#label_role {\n"
"	color: #9CA2AE;\n"
"}")
        self.label_roleText = QLabel(self.Item_left)
        self.label_roleText.setObjectName(u"label_roleText")
        self.label_roleText.setGeometry(QRect(120, 60, 81, 16))
        self.label_roleText.setFont(font1)
        self.label_pinText = QLabel(self.Item_left)
        self.label_pinText.setObjectName(u"label_pinText")
        self.label_pinText.setGeometry(QRect(32, 60, 51, 16))
        self.label_pinText.setFont(font1)
        self.label_pin = QLabel(self.Item_left)
        self.label_pin.setObjectName(u"label_pin")
        self.label_pin.setGeometry(QRect(0, 60, 31, 16))
        self.label_pin.setStyleSheet(u"#label_pin {\n"
"	color: #9CA2AE;\n"
"}")
        self.label_password = QLabel(self.Item_left)
        self.label_password.setObjectName(u"label_password")
        self.label_password.setGeometry(QRect(0, 30, 31, 16))
        self.label_password.setStyleSheet(u"#label_password {\n"
"	color: #9CA2AE;\n"
"}")

        self.horizontalLayout_7.addWidget(self.Item_left, 0, Qt.AlignmentFlag.AlignLeft)

        self.Item_middle = QWidget(self.accountItem)
        self.Item_middle.setObjectName(u"Item_middle")
        sizePolicy2.setHeightForWidth(self.Item_middle.sizePolicy().hasHeightForWidth())
        self.Item_middle.setSizePolicy(sizePolicy2)
        self.Item_middle.setMinimumSize(QSize(170, 0))
        self.label_runtimeText = QLabel(self.Item_middle)
        self.label_runtimeText.setObjectName(u"label_runtimeText")
        self.label_runtimeText.setGeometry(QRect(60, 60, 50, 20))
        self.label_runtimeText.setFont(font1)
        self.label_runtimeText.setStyleSheet(u"")
        self.label_runtime = QLabel(self.Item_middle)
        self.label_runtime.setObjectName(u"label_runtime")
        self.label_runtime.setGeometry(QRect(0, 60, 51, 16))
        self.label_runtime.setStyleSheet(u"#label_runtime {\n"
"	color: #9CA2AE;\n"
"}")
        self.label_lastout = QLabel(self.Item_middle)
        self.label_lastout.setObjectName(u"label_lastout")
        self.label_lastout.setGeometry(QRect(0, 32, 51, 16))
        self.label_lastout.setStyleSheet(u"#label_lastout{\n"
"	color: #9CA2AE;\n"
"}")
        self.label_lastout_2 = QLabel(self.Item_middle)
        self.label_lastout_2.setObjectName(u"label_lastout_2")
        self.label_lastout_2.setGeometry(QRect(60, 30, 112, 20))
        self.label_lastout_2.setFont(font1)
        self.label_lastout_2.setStyleSheet(u"")
        self.label_lastlogin = QLabel(self.Item_middle)
        self.label_lastlogin.setObjectName(u"label_lastlogin")
        self.label_lastlogin.setGeometry(QRect(0, -2, 51, 16))
        self.label_lastlogin.setStyleSheet(u"#label_lastlogin {\n"
"	color: #9CA2AE;\n"
"}")
        self.label_lastloginText = QLabel(self.Item_middle)
        self.label_lastloginText.setObjectName(u"label_lastloginText")
        self.label_lastloginText.setGeometry(QRect(60, -4, 112, 20))
        self.label_lastloginText.setFont(font1)
        self.label_lastloginText.setStyleSheet(u"")

        self.horizontalLayout_7.addWidget(self.Item_middle, 0, Qt.AlignmentFlag.AlignLeft)

        self.label_state = QLabel(self.accountItem)
        self.label_state.setObjectName(u"label_state")
        sizePolicy2.setHeightForWidth(self.label_state.sizePolicy().hasHeightForWidth())
        self.label_state.setSizePolicy(sizePolicy2)
        self.label_state.setMinimumSize(QSize(80, 0))
        self.label_state.setFont(font7)
        self.label_state.setStyleSheet(u"#label_state {\n"
"	color:#4169E1;\n"
"}")

        self.horizontalLayout_7.addWidget(self.label_state, 0, Qt.AlignmentFlag.AlignLeft)

        self.Item_right = QWidget(self.accountItem)
        self.Item_right.setObjectName(u"Item_right")
        sizePolicy2.setHeightForWidth(self.Item_right.sizePolicy().hasHeightForWidth())
        self.Item_right.setSizePolicy(sizePolicy2)
        self.Item_right.setMinimumSize(QSize(80, 0))
        self.btn_delete = QPushButton(self.Item_right)
        self.btn_delete.setObjectName(u"btn_delete")
        self.btn_delete.setGeometry(QRect(14, 54, 60, 26))
        self.btn_delete.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_delete.setStyleSheet(u"#btn_delete {\n"
"	background-color:#D32F2F;\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 12px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_delete:hover {\n"
"    background-color: #EF5350; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_modify = QPushButton(self.Item_right)
        self.btn_modify.setObjectName(u"btn_modify")
        self.btn_modify.setGeometry(QRect(14, 18, 60, 26))
        self.btn_modify.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_modify.setStyleSheet(u"#btn_modify {\n"
"	background-color:#3A3E47;\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"    font-size: 12px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"}\n"
"\n"
"#btn_modify:hover {\n"
"    background-color: #21252D; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")

        self.horizontalLayout_7.addWidget(self.Item_right, 0, Qt.AlignmentFlag.AlignLeft)

        self.horizontalLayout_7.setStretch(0, 2)
        self.horizontalLayout_7.setStretch(1, 2)
        self.horizontalLayout_7.setStretch(2, 2)
        self.horizontalLayout_7.setStretch(3, 1)

        self.verticalLayout_12.addWidget(self.accountItem, 0, Qt.AlignmentFlag.AlignTop)

        self.scrollArea_account.setWidget(self.saa_Layout)

        self.verticalLayout_10.addWidget(self.scrollArea_account)

        self.stackedWidget.addWidget(self.page_account)
        self.page_functions = QWidget()
        self.page_functions.setObjectName(u"page_functions")
        self.verticalLayout_2 = QVBoxLayout(self.page_functions)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.scrollArea_functions = QScrollArea(self.page_functions)
        self.scrollArea_functions.setObjectName(u"scrollArea_functions")
        self.scrollArea_functions.setWidgetResizable(True)
        self.saf_Layout = QWidget()
        self.saf_Layout.setObjectName(u"saf_Layout")
        self.saf_Layout.setGeometry(QRect(0, 0, 98, 28))
        self.label = QLabel(self.saf_Layout)
        self.label.setObjectName(u"label")
        self.label.setGeometry(QRect(260, 300, 54, 16))
        self.scrollArea_functions.setWidget(self.saf_Layout)

        self.verticalLayout_2.addWidget(self.scrollArea_functions)

        self.stackedWidget.addWidget(self.page_functions)
        self.page_settings = QWidget()
        self.page_settings.setObjectName(u"page_settings")
        self.stackedWidget.addWidget(self.page_settings)
        self.page_about = QWidget()
        self.page_about.setObjectName(u"page_about")
        self.verticalLayout_3 = QVBoxLayout(self.page_about)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(-1, 0, -1, -1)
        self.about_head = QFrame(self.page_about)
        self.about_head.setObjectName(u"about_head")
        sizePolicy4 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Maximum)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.about_head.sizePolicy().hasHeightForWidth())
        self.about_head.setSizePolicy(sizePolicy4)
        self.about_head.setMinimumSize(QSize(0, 320))
        self.about_head.setStyleSheet(u"#about_head {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    margin:10px 10px 15px 8px;\n"
"    border: 1px solid #20242A; /* \u8fb9\u6846\u5206\u9694\u7ebf */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}\n"
"")
        self.about_head.setFrameShape(QFrame.Shape.StyledPanel)
        self.about_head.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_4 = QVBoxLayout(self.about_head)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.head_1 = QWidget(self.about_head)
        self.head_1.setObjectName(u"head_1")
        self.head_1.setStyleSheet(u"#head_1 {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"   	border-top: none;\n"
"    border-right: none;\n"
"    border-bottom: 1px solid #292C33;\n"
"    border-left: none;\n"
"    margin:0 15px 15px 15px;\n"
"}\n"
"\n"
"")
        self.horizontalLayout_2 = QHBoxLayout(self.head_1)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.head_1_left = QWidget(self.head_1)
        self.head_1_left.setObjectName(u"head_1_left")
        sizePolicy2.setHeightForWidth(self.head_1_left.sizePolicy().hasHeightForWidth())
        self.head_1_left.setSizePolicy(sizePolicy2)
        self.head_1_left.setMinimumSize(QSize(200, 0))
        self.head_1_left.setStyleSheet(u"#head_1_left {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	margin:0 0 10px 0;\n"
"}")
        self.head_1_name = QLabel(self.head_1_left)
        self.head_1_name.setObjectName(u"head_1_name")
        self.head_1_name.setGeometry(QRect(6, 50, 181, 31))
        font9 = QFont()
        font9.setFamilies([u"Microsoft YaHei"])
        font9.setPointSize(13)
        font9.setBold(True)
        self.head_1_name.setFont(font9)
        self.head_1_name.setStyleSheet(u"#head_1_name {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")
        self.head_1_about = QLabel(self.head_1_left)
        self.head_1_about.setObjectName(u"head_1_about")
        self.head_1_about.setGeometry(QRect(0, 0, 111, 36))
        font10 = QFont()
        font10.setFamilies([u"Microsoft YaHei"])
        font10.setPointSize(20)
        font10.setBold(True)
        self.head_1_about.setFont(font10)
        self.head_1_about.setStyleSheet(u"#head_1_about {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")

        self.horizontalLayout_2.addWidget(self.head_1_left)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)

        self.head_1_right = QWidget(self.head_1)
        self.head_1_right.setObjectName(u"head_1_right")
        sizePolicy2.setHeightForWidth(self.head_1_right.sizePolicy().hasHeightForWidth())
        self.head_1_right.setSizePolicy(sizePolicy2)
        self.head_1_right.setMinimumSize(QSize(132, 0))
        self.head_1_right.setStyleSheet(u"#head_1_right {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	margin:0 0 10px 0;\n"
"}")
        self.head_1_by = QLabel(self.head_1_right)
        self.head_1_by.setObjectName(u"head_1_by")
        self.head_1_by.setGeometry(QRect(0, 60, 132, 21))
        self.head_1_by.setStyleSheet(u"#head_1_by {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")

        self.horizontalLayout_2.addWidget(self.head_1_right)


        self.verticalLayout_4.addWidget(self.head_1)

        self.head_2 = QFrame(self.about_head)
        self.head_2.setObjectName(u"head_2")
        self.head_2.setStyleSheet(u"#head_2 {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.head_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.head_2.setFrameShadow(QFrame.Shadow.Raised)
        self.head_2_copyrightText = QLabel(self.head_2)
        self.head_2_copyrightText.setObjectName(u"head_2_copyrightText")
        self.head_2_copyrightText.setGeometry(QRect(50, 0, 211, 20))
        font11 = QFont()
        font11.setFamilies([u"Microsoft YaHei"])
        font11.setPointSize(8)
        self.head_2_copyrightText.setFont(font11)
        self.head_2_copyrightText.setStyleSheet(u"#head_2_copyrightText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.head_2_emailText = QLabel(self.head_2)
        self.head_2_emailText.setObjectName(u"head_2_emailText")
        self.head_2_emailText.setGeometry(QRect(50, 40, 61, 20))
        self.head_2_emailText.setFont(font11)
        self.head_2_emailText.setStyleSheet(u"#head_2_emailText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.head_2_qqText = QLabel(self.head_2)
        self.head_2_qqText.setObjectName(u"head_2_qqText")
        self.head_2_qqText.setGeometry(QRect(50, 80, 71, 20))
        self.head_2_qqText.setFont(font11)
        self.head_2_qqText.setStyleSheet(u"#head_2_qqText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.head_2_copyright = QLabel(self.head_2)
        self.head_2_copyright.setObjectName(u"head_2_copyright")
        self.head_2_copyright.setGeometry(QRect(20, 0, 16, 20))
        font12 = QFont()
        font12.setFamilies([u"Microsoft YaHei"])
        font12.setPointSize(9)
        self.head_2_copyright.setFont(font12)
        self.head_2_copyright.setStyleSheet(u"#head_2_copyright {\n"
"    background-color: transparent; \n"
"}")
        self.head_2_email = QLabel(self.head_2)
        self.head_2_email.setObjectName(u"head_2_email")
        self.head_2_email.setGeometry(QRect(20, 40, 21, 20))
        font13 = QFont()
        font13.setFamilies([u"Microsoft YaHei"])
        font13.setPointSize(10)
        self.head_2_email.setFont(font13)
        self.head_2_email.setStyleSheet(u"#head_2_email {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.head_2_qq = QLabel(self.head_2)
        self.head_2_qq.setObjectName(u"head_2_qq")
        self.head_2_qq.setGeometry(QRect(20, 80, 21, 20))
        self.head_2_qq.setFont(font13)
        self.head_2_qq.setStyleSheet(u"#head_2_qq {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")

        self.verticalLayout_4.addWidget(self.head_2)

        self.button_thank = QPushButton(self.about_head)
        self.button_thank.setObjectName(u"button_thank")
        self.button_thank.setMinimumSize(QSize(0, 30))
        self.button_thank.setStyleSheet(u"#button_thank {\n"
"	background-color: rgb(31, 45, 48);\n"
"    border-radius: 8px; /* \u5706\u89d2 */\n"
"	color: #9CA2AE;\n"
"    margin:0 15px 0 15px;\n"
"}")

        self.verticalLayout_4.addWidget(self.button_thank)


        self.verticalLayout_3.addWidget(self.about_head)

        self.about_body = QWidget(self.page_about)
        self.about_body.setObjectName(u"about_body")
        self.about_body.setStyleSheet(u"#about_body {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    color: #e0e0e0;            /* \u6d45\u7070\u8272\u6587\u5b57 */\n"
"    margin:0 10px 80px 8px;\n"
"    border: 1px solid #20242A; /* \u8fb9\u6846\u5206\u9694\u7ebf */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"")
        self.verticalLayout_11 = QVBoxLayout(self.about_body)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.body_1 = QWidget(self.about_body)
        self.body_1.setObjectName(u"body_1")
        sizePolicy.setHeightForWidth(self.body_1.sizePolicy().hasHeightForWidth())
        self.body_1.setSizePolicy(sizePolicy)
        self.body_1.setMinimumSize(QSize(0, 50))
        self.body_1.setStyleSheet(u"#body_1 {\n"
"    background-color: transparent; \n"
"}")
        self.horizontalLayout_3 = QHBoxLayout(self.body_1)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.body_1_left = QWidget(self.body_1)
        self.body_1_left.setObjectName(u"body_1_left")
        sizePolicy2.setHeightForWidth(self.body_1_left.sizePolicy().hasHeightForWidth())
        self.body_1_left.setSizePolicy(sizePolicy2)
        self.body_1_left.setMinimumSize(QSize(160, 0))
        self.body_1_left.setStyleSheet(u"#body_1_left {\n"
"	background-color: transparent; \n"
"}")
        self.body_TitleText = QLabel(self.body_1_left)
        self.body_TitleText.setObjectName(u"body_TitleText")
        self.body_TitleText.setGeometry(QRect(20, 0, 140, 31))
        font14 = QFont()
        font14.setFamilies([u"Microsoft YaHei"])
        font14.setPointSize(18)
        font14.setBold(True)
        self.body_TitleText.setFont(font14)
        self.body_TitleText.setStyleSheet(u"#body_TitleText {\n"
"    background-color: transparent; \n"
"	color:white\uff1b\n"
"}")

        self.horizontalLayout_3.addWidget(self.body_1_left)

        self.horizontalSpacer_2 = QSpacerItem(393, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_2)

        self.body_1_right = QWidget(self.body_1)
        self.body_1_right.setObjectName(u"body_1_right")
        sizePolicy2.setHeightForWidth(self.body_1_right.sizePolicy().hasHeightForWidth())
        self.body_1_right.setSizePolicy(sizePolicy2)
        self.body_1_right.setMinimumSize(QSize(160, 0))
        self.body_1_right.setStyleSheet(u"#body_1_right {\n"
"	background-color: transparent; \n"
"}")
        self.body_copyText = QLabel(self.body_1_right)
        self.body_copyText.setObjectName(u"body_copyText")
        self.body_copyText.setGeometry(QRect(0, 12, 160, 20))
        self.body_copyText.setStyleSheet(u"#body_copyText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")

        self.horizontalLayout_3.addWidget(self.body_1_right)


        self.verticalLayout_11.addWidget(self.body_1)

        self.body_2 = QWidget(self.about_body)
        self.body_2.setObjectName(u"body_2")
        self.body_2.setStyleSheet(u"#body_2 {\n"
"    background-color: transparent; \n"
"\n"
"}")
        self.body_bugText = QLabel(self.body_2)
        self.body_bugText.setObjectName(u"body_bugText")
        self.body_bugText.setGeometry(QRect(30, 130, 601, 20))
        self.body_bugText.setFont(font13)
        self.body_bugText.setStyleSheet(u"#body_bugText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.body_documentText = QLabel(self.body_2)
        self.body_documentText.setObjectName(u"body_documentText")
        self.body_documentText.setGeometry(QRect(30, 10, 601, 20))
        self.body_documentText.setFont(font13)
        self.body_documentText.setStyleSheet(u"#body_documentText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.body_updateLogText = QLabel(self.body_2)
        self.body_updateLogText.setObjectName(u"body_updateLogText")
        self.body_updateLogText.setGeometry(QRect(30, 50, 601, 20))
        self.body_updateLogText.setFont(font13)
        self.body_updateLogText.setStyleSheet(u"#body_updateLogText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")
        self.body_HelpText = QLabel(self.body_2)
        self.body_HelpText.setObjectName(u"body_HelpText")
        self.body_HelpText.setGeometry(QRect(30, 90, 601, 20))
        self.body_HelpText.setFont(font13)
        self.body_HelpText.setStyleSheet(u"#body_HelpText {\n"
"    background-color: transparent; \n"
"	color: #9CA2AE;\n"
"}")

        self.verticalLayout_11.addWidget(self.body_2)


        self.verticalLayout_3.addWidget(self.about_body)

        self.stackedWidget.addWidget(self.page_about)
        self.page_logs = QWidget()
        self.page_logs.setObjectName(u"page_logs")
        self.page_logs.setStyleSheet(u"#page_logs {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    color: #e0e0e0;            /* \u6d45\u7070\u8272\u6587\u5b57 */\n"
"    border: 1px solid #20242A; /* \u8fb9\u6846\u5206\u9694\u7ebf */\n"
"    border-radius: 12px; /* \u5706\u89d2 */\n"
"}\n"
"")
        self.verticalLayout_5 = QVBoxLayout(self.page_logs)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.logs_head = QFrame(self.page_logs)
        self.logs_head.setObjectName(u"logs_head")
        sizePolicy.setHeightForWidth(self.logs_head.sizePolicy().hasHeightForWidth())
        self.logs_head.setSizePolicy(sizePolicy)
        self.logs_head.setMinimumSize(QSize(0, 80))
        self.logs_head.setStyleSheet(u"#logs_head {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}\n"
"")
        self.logs_head.setFrameShape(QFrame.Shape.StyledPanel)
        self.logs_head.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_8 = QHBoxLayout(self.logs_head)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalLayout_8.setContentsMargins(0, -1, -1, -1)
        self.logs_head_left = QWidget(self.logs_head)
        self.logs_head_left.setObjectName(u"logs_head_left")
        sizePolicy2.setHeightForWidth(self.logs_head_left.sizePolicy().hasHeightForWidth())
        self.logs_head_left.setSizePolicy(sizePolicy2)
        self.logs_head_left.setMinimumSize(QSize(420, 0))
        self.logs_head_left.setStyleSheet(u"#logs_head_left {\n"
"    background-color: transparent; \n"
"}")
        self.btn_info = QPushButton(self.logs_head_left)
        self.btn_info.setObjectName(u"btn_info")
        self.btn_info.setGeometry(QRect(26, 22, 68, 26))
        self.btn_info.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_info.setStyleSheet(u"#btn_info {\n"
"	color: #9E9E9E;\n"
"	background-color: #1B1E24; \n"
"	border: 1px solid #9E9E9E;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"#btn_info:hover {\n"
"	background-color: rgba(158, 158, 158, 0.2);\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.btn_error = QPushButton(self.logs_head_left)
        self.btn_error.setObjectName(u"btn_error")
        self.btn_error.setGeometry(QRect(186, 22, 68, 26))
        self.btn_error.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_error.setStyleSheet(u"#btn_error {\n"
"	color: #FF6B6B;\n"
"	background-color: #1B1E24;\n"
"	border: 1px solid #FF6B6B;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"#btn_error:hover {\n"
"	background-color: rgba(255, 107, 107, 0.2);\n"
"}")
        self.btn_warning = QPushButton(self.logs_head_left)
        self.btn_warning.setObjectName(u"btn_warning")
        self.btn_warning.setGeometry(QRect(106, 22, 68, 26))
        self.btn_warning.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_warning.setStyleSheet(u"#btn_warning {\n"
"	color: #FFE24D;\n"
"	background-color: #1B1E24;\n"
"	border: 1px solid #FFE24D;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"#btn_warning:hover {\n"
"	background-color: rgba(255, 226, 77, 0.2);\n"
"}\n"
"")
        self.btn_debug = QPushButton(self.logs_head_left)
        self.btn_debug.setObjectName(u"btn_debug")
        self.btn_debug.setGeometry(QRect(268, 22, 68, 26))
        self.btn_debug.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_debug.setStyleSheet(u"#btn_debug {\n"
"	color: #3875FF;\n"
"	background-color: #1B1E24;\n"
"	border: 1px solid #3875FF;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"#btn_debug:hover {\n"
"	background-color: rgba(56, 123, 255, 0.2);\n"
"}")
        self.btn_critical = QPushButton(self.logs_head_left)
        self.btn_critical.setObjectName(u"btn_critical")
        self.btn_critical.setGeometry(QRect(348, 22, 68, 26))
        self.btn_critical.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_critical.setStyleSheet(u"#btn_critical {\n"
"	color: #FF4757;\n"
"	background-color: #1B1E24;\n"
"	border: 1px solid #FF4757;\n"
"	border-radius: 10px;\n"
"}\n"
"\n"
"#btn_critical:hover {\n"
"	background-color: rgba(255, 71, 87, 0.2);\n"
"}")

        self.horizontalLayout_8.addWidget(self.logs_head_left)

        self.horizontalSpacer_10 = QSpacerItem(96, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_10)

        self.logs_head_right = QWidget(self.logs_head)
        self.logs_head_right.setObjectName(u"logs_head_right")
        sizePolicy2.setHeightForWidth(self.logs_head_right.sizePolicy().hasHeightForWidth())
        self.logs_head_right.setSizePolicy(sizePolicy2)
        self.logs_head_right.setMinimumSize(QSize(222, 0))
        self.logs_head_right.setStyleSheet(u"#logs_head_right {\n"
"    background-color: transparent; \n"
"}")
        self.btn_refreshLogs = QPushButton(self.logs_head_right)
        self.btn_refreshLogs.setObjectName(u"btn_refreshLogs")
        self.btn_refreshLogs.setGeometry(QRect(126, 14, 94, 36))
        self.btn_refreshLogs.setFont(font1)
        self.btn_refreshLogs.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_refreshLogs.setStyleSheet(u"#btn_refreshLogs {\n"
"	color: #FFFFFF;\n"
"    background-color: #2B9D7C; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#btn_refreshLogs:hover {\n"
"    background-color: #34B892; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}")
        self.btn_pathLogs = QPushButton(self.logs_head_right)
        self.btn_pathLogs.setObjectName(u"btn_pathLogs")
        self.btn_pathLogs.setGeometry(QRect(0, 14, 110, 34))
        self.btn_pathLogs.setFont(font1)
        self.btn_pathLogs.setCursor(QCursor(Qt.CursorShape.PointingHandCursor))
        self.btn_pathLogs.setStyleSheet(u"#btn_pathLogs {\n"
"	color: #FFFFFF;\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    border: 1px solid #292C33;\n"
"    border-radius: 10px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"#btn_pathLogs:hover {\n"
"    background-color: #D6B16A;\n"
"}\n"
"")

        self.horizontalLayout_8.addWidget(self.logs_head_right)


        self.verticalLayout_5.addWidget(self.logs_head)

        self.logs_body = QFrame(self.page_logs)
        self.logs_body.setObjectName(u"logs_body")
        self.logs_body.setCursor(QCursor(Qt.CursorShape.ArrowCursor))
        self.logs_body.setStyleSheet(u"#logs_body {\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"}\n"
"")
        self.logs_body.setFrameShape(QFrame.Shape.StyledPanel)
        self.logs_body.setFrameShadow(QFrame.Shadow.Raised)
        self.verticalLayout_7 = QVBoxLayout(self.logs_body)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.logs_text = QPlainTextEdit(self.logs_body)
        self.logs_text.setObjectName(u"logs_text")
        font15 = QFont()
        font15.setFamilies([u"Consolas"])
        font15.setBold(False)
        self.logs_text.setFont(font15)
        self.logs_text.viewport().setProperty(u"cursor", QCursor(Qt.CursorShape.ArrowCursor))
        self.logs_text.setStyleSheet(u"/* ===== \u57fa\u7840\u6837\u5f0f ===== */\n"
"#logs_text {\n"
"    /* \u80cc\u666f\u548c\u8fb9\u6846 */\n"
"    background-color: #1B1E24; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"	/* \u9009\u4e2d\u6587\u672c\u7684\u6837\u5f0f */\n"
"    selection-background-color: #2B9D7C;\n"
"	font-family: \"Consolas\";\n"
"    \n"
"/* ===== \u6587\u672c\u5185\u5bb9\u6837\u5f0f ===== */\n"
"    /* \u6587\u672c\u6837\u5f0f */\n"
"    color: #9E9E9E;             /* \u6587\u672c\u989c\u8272 */\n"
"    font-size: 14px;            /* \u5b57\u4f53\u5927\u5c0f */\n"
"    \n"
"    /* \u6587\u672c\u8fb9\u8ddd */\n"
"    padding-right: 15px;         /* \u53f3\u5185\u8fb9\u8ddd */\n"
"    padding-bottom: 20px;        /* \u4e0b\u5185\u8fb9\u8ddd */\n"
"    padding-left: 15px;          /* \u5de6\u5185\u8fb9\u8ddd */\n"
"    \n"
"       /* \u5b57\u4f53\u548c\u884c\u95f4\u8ddd */\n"
"    font-family: \"Consolas\";\n"
"    font-size: 14px;\n"
"    line-height: 5;                    /* \u884c\u95f4\u8ddd\u500d\u6570 */\n"
"    \n"
"    /* \u5b57"
                        "\u7b26\u95f4\u8ddd */\n"
"    letter-spacing: 1px;               \n"
"}\n"
"\n"
"/* ===== \u6eda\u52a8\u6761\u6837\u5f0f ===== */\n"
"/* \u5782\u76f4\u6eda\u52a8\u6761 */\n"
"#logs_text QScrollBar:vertical {\n"
"    border: none;               /* \u65e0\u8fb9\u6846 */\n"
"    background-color: #2A2E36; /* \u6df1\u84dd\u7070\u80cc\u666f */\n"
"    width: 10px;                /* \u6eda\u52a8\u6761\u5bbd\u5ea6 */\n"
"    border-radius: 4px; /* \u5706\u89d2 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u624b\u67c4 */\n"
"#logs_text QScrollBar::handle:vertical {\n"
"    background: #4A4D56;        /* \u624b\u67c4\u989c\u8272 */\n"
"    min-height: 20px;            /* \u6700\u5c0f\u9ad8\u5ea6 */\n"
"    border-radius: 5px;          /* \u5706\u89d2 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u624b\u67c4\u60ac\u505c\u6548\u679c */\n"
"#logs_text QScrollBar::handle:vertical:hover {\n"
"    background: #5A5D66;        /* \u60ac\u505c\u989c\u8272 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u624b\u67c4\u6309\u4e0b\u6548\u679c */\n"
"#l"
                        "ogs_text QScrollBar::handle:vertical:pressed {\n"
"    background: #5A5D66;        /* \u6309\u4e0b\u989c\u8272 */\n"
"}\n"
"\n"
"/* \u6eda\u52a8\u6761\u6309\u94ae\uff08\u4e0a\u4e0b\u7bad\u5934\uff09 */\n"
"#logs_text QScrollBar::add-line:vertical, \n"
"#logs_text QScrollBar::sub-line:vertical,\n"
"#logs_text QScrollBar::add-line:horizontal, \n"
"#logs_text QScrollBar::sub-line:horizontal {\n"
"	background: none;        /* \u6309\u4e0b\u989c\u8272 */\n"
"}\n"
"\n"
"/* \u9690\u85cf\u6eda\u52a8\u6761\u69fd */\n"
"#logs_text QScrollBar::add-page:vertical, \n"
"#logs_text QScrollBar::sub-page:vertical,\n"
"#logs_text QScrollBar::add-page:horizontal, \n"
"#logs_text QScrollBar::sub-page:horizontal {\n"
"    background: none;\n"
"}\n"
"")
        self.logs_text.setReadOnly(True)

        self.verticalLayout_7.addWidget(self.logs_text)


        self.verticalLayout_5.addWidget(self.logs_body)

        self.stackedWidget.addWidget(self.page_logs)

        self.horizontalLayout_5.addWidget(self.stackedWidget)


        self.horizontalLayout.addLayout(self.horizontalLayout_5)


        self.verticalLayout_8.addWidget(self.InnerWidget)


        self.retranslateUi(MainForm)

        self.listWidget.setCurrentRow(0)
        self.stackedWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainForm)
    # setupUi

    def retranslateUi(self, MainForm):
        MainForm.setWindowTitle(QCoreApplication.translate("MainForm", u"Form", None))
        self.btn_close.setText("")
        self.btn_maximize.setText("")
        self.btn_minimize.setText("")
        self.appTitle.setText(QCoreApplication.translate("MainForm", u"BetterElsrift", None))

        __sortingEnabled = self.listWidget.isSortingEnabled()
        self.listWidget.setSortingEnabled(False)
        ___qlistwidgetitem = self.listWidget.item(0)
        ___qlistwidgetitem.setText(QCoreApplication.translate("MainForm", u"\u9996\u9875", None));
        ___qlistwidgetitem1 = self.listWidget.item(1)
        ___qlistwidgetitem1.setText(QCoreApplication.translate("MainForm", u"\u8d26\u53f7", None));
        ___qlistwidgetitem2 = self.listWidget.item(2)
        ___qlistwidgetitem2.setText(QCoreApplication.translate("MainForm", u"\u529f\u80fd", None));
        ___qlistwidgetitem3 = self.listWidget.item(3)
        ___qlistwidgetitem3.setText(QCoreApplication.translate("MainForm", u"\u8bbe\u7f6e", None));
        ___qlistwidgetitem4 = self.listWidget.item(4)
        ___qlistwidgetitem4.setText(QCoreApplication.translate("MainForm", u"\u5173\u4e8e", None));
        ___qlistwidgetitem5 = self.listWidget.item(5)
        ___qlistwidgetitem5.setText(QCoreApplication.translate("MainForm", u"\u65e5\u5fd7", None));
        self.listWidget.setSortingEnabled(__sortingEnabled)

        self.label_1.setText(QCoreApplication.translate("MainForm", u"SandBox-Plus v1.15.12 ", None))
        self.sandBoxSign.setText("")
        self.label_2.setText(QCoreApplication.translate("MainForm", u"BetterElsrift  v0.7.1", None))
        self.system_time.setText(QCoreApplication.translate("MainForm", u"\u7cfb\u7edf\u65f6\u95f4\uff1a2025/7/15 10:26", None))
        self.elsriftSign.setText("")
        self.label_3.setText(QCoreApplication.translate("MainForm", u"Elsrift", None))
        self.btn_run.setText(QCoreApplication.translate("MainForm", u"\u5f00\u59cb", None))
        self.label_acc_1.setText(QCoreApplication.translate("MainForm", u"\u8d26\u53f71\uff1a", None))
        self.comboBox_1.setItemText(0, QCoreApplication.translate("MainForm", u"\u65b0\u5efa\u9879\u76ee", None))
        self.comboBox_1.setItemText(1, QCoreApplication.translate("MainForm", u"\u65b0\u5efa\u9879\u76ee", None))
        self.comboBox_1.setItemText(2, QCoreApplication.translate("MainForm", u"\u65b0\u5efa\u9879\u76ee", None))
        self.comboBox_1.setItemText(3, QCoreApplication.translate("MainForm", u"\u65b0\u5efa\u9879\u76ee", None))
        self.comboBox_1.setItemText(4, QCoreApplication.translate("MainForm", u"\u65b0\u5efa\u9879\u76ee", None))

        self.btn_del.setText(QCoreApplication.translate("MainForm", u"\u5220\u9664", None))
        self.btn_initialize_sandbox.setText(QCoreApplication.translate("MainForm", u"\u521d\u59cb\u5316", None))
        self.label_nums.setText(QCoreApplication.translate("MainForm", u"\u2699\ufe0f  \u591a\u5f00\u6570\u91cf\uff1a", None))
        self.btn_add.setText(QCoreApplication.translate("MainForm", u"-", None))
        self.btn_minus.setText(QCoreApplication.translate("MainForm", u"+", None))
        self.lineEdit_count.setInputMask("")
        self.lineEdit_count.setText(QCoreApplication.translate("MainForm", u"1", None))
        self.lineEdit_count.setPlaceholderText("")
        self.head_info.setText(QCoreApplication.translate("MainForm", u"\u5171 1 \u4e2a\u8d26\u6237\uff08 0 \u4e2a\u542f\u7528\uff0c1 \u4e2a\u542f\u7528\uff0c0 \u4e2a\u51b7\u5374\u4e2d\uff09", None))
        self.btn_refreshState.setText(QCoreApplication.translate("MainForm", u"\u5237\u65b0\u72b6\u6001", None))
        self.btn_addAccount.setText(QCoreApplication.translate("MainForm", u"\u6dfb\u52a0\u8d26\u6237", None))
        self.btn_export.setText(QCoreApplication.translate("MainForm", u"\u5bfc\u51fa\u8d26\u6237", None))
        self.title_account.setText(QCoreApplication.translate("MainForm", u"\u8d26\u6237\u4fe1\u606f", None))
        self.title_realdata.setText(QCoreApplication.translate("MainForm", u"\u8d26\u6237\u5b9e\u51b5", None))
        self.title_state.setText(QCoreApplication.translate("MainForm", u"\u8d26\u6237\u72b6\u6001", None))
        self.title_operate.setText(QCoreApplication.translate("MainForm", u"\u64cd\u4f5c", None))
        self.label_account.setText(QCoreApplication.translate("MainForm", u"<EMAIL>", None))
        self.label_passwordText.setText(QCoreApplication.translate("MainForm", u"ASDasd123. ", None))
        self.label_role.setText(QCoreApplication.translate("MainForm", u"\u89d2\u8272\uff1a", None))
        self.label_roleText.setText(QCoreApplication.translate("MainForm", u"CS", None))
        self.label_pinText.setText(QCoreApplication.translate("MainForm", u"999999", None))
        self.label_pin.setText(QCoreApplication.translate("MainForm", u"PIN\uff1a", None))
        self.label_password.setText(QCoreApplication.translate("MainForm", u"\u5bc6\u7801\uff1a", None))
        self.label_runtimeText.setText(QCoreApplication.translate("MainForm", u"7.2h", None))
        self.label_runtime.setText(QCoreApplication.translate("MainForm", u"\u8fd0\u884c\u65f6\u95f4:", None))
        self.label_lastout.setText(QCoreApplication.translate("MainForm", u"\u6700\u540e\u9000\u51fa:", None))
        self.label_lastout_2.setText(QCoreApplication.translate("MainForm", u"2025/7/15 11:55", None))
        self.label_lastlogin.setText(QCoreApplication.translate("MainForm", u"\u6700\u540e\u767b\u5f55\uff1a", None))
        self.label_lastloginText.setText(QCoreApplication.translate("MainForm", u"2025/7/15 11:55", None))
        self.label_state.setText(QCoreApplication.translate("MainForm", u"\u8fd0\u884c\u4e2d", None))
        self.btn_delete.setText(QCoreApplication.translate("MainForm", u"\u5220\u9664", None))
        self.btn_modify.setText(QCoreApplication.translate("MainForm", u"\u4fee\u6539", None))
        self.label.setText(QCoreApplication.translate("MainForm", u"TextLabel", None))
        self.head_1_name.setText(QCoreApplication.translate("MainForm", u"BetterElsrift v0.7.1", None))
        self.head_1_about.setText(QCoreApplication.translate("MainForm", u"\u2728 \u5173\u4e8e", None))
        self.head_1_by.setText(QCoreApplication.translate("MainForm", u"Developed by SLMagic", None))
        self.head_2_copyrightText.setText(QCoreApplication.translate("MainForm", u"2025 SLMagic - All rights reserved", None))
        self.head_2_emailText.setText(QCoreApplication.translate("MainForm", u"SLMagic@", None))
        self.head_2_qqText.setText(QCoreApplication.translate("MainForm", u"QQ\u7fa4\uff1a111", None))
        self.head_2_copyright.setText(QCoreApplication.translate("MainForm", u"\u00a9\ufe0f", None))
        self.head_2_email.setText(QCoreApplication.translate("MainForm", u"📧", None))
        self.head_2_qq.setText(QCoreApplication.translate("MainForm", u"👥", None))
        self.button_thank.setText(QCoreApplication.translate("MainForm", u"感谢您的使用和支持❤️", None))
        self.body_TitleText.setText(QCoreApplication.translate("MainForm", u"📚 相关文档", None))
        self.body_copyText.setText(QCoreApplication.translate("MainForm", u"左键点击跳转，右键点击复制", None))
        self.body_bugText.setText(QCoreApplication.translate("MainForm", u"🤖 版本BUG通知文档：", None))
        self.body_documentText.setText(QCoreApplication.translate("MainForm", u"📖 官方文档：", None))
        self.body_updateLogText.setText(QCoreApplication.translate("MainForm", u"📜 更新日志：", None))
        self.body_HelpText.setText(QCoreApplication.translate("MainForm", u"💡 帮助，问题文档：", None))
        self.btn_info.setText(QCoreApplication.translate("MainForm", u"\u4fe1\u606f", None))
        self.btn_error.setText(QCoreApplication.translate("MainForm", u"\u9519\u8bef", None))
        self.btn_warning.setText(QCoreApplication.translate("MainForm", u"\u8b66\u544a", None))
        self.btn_debug.setText(QCoreApplication.translate("MainForm", u"\u8c03\u8bd5", None))
        self.btn_critical.setText(QCoreApplication.translate("MainForm", u"\u4e25\u91cd", None))
        self.btn_refreshLogs.setText(QCoreApplication.translate("MainForm", u"\u5237\u65b0\u65e5\u5fd7", None))
        self.btn_pathLogs.setText(QCoreApplication.translate("MainForm", u"\u65e5\u5fd7\u76ee\u5f55", None))
        self.logs_text.setPlainText(QCoreApplication.translate("MainForm", u"", None))
    # retranslateUi

