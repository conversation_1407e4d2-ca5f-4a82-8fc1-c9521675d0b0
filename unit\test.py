import threading
import numpy as np
import win32gui, win32ui, win32con
import math
from ctypes import windll
from mylogger.MyLogger import My<PERSON>ogger
from mss import mss
import os
from paddleocr import PaddleOCR
import cv2
from automation.myutils.configutils import resource_path

logger = MyLogger('window_capture_test')

# OCR模型路径配置
det_dir = os.path.join(resource_path, 'ocr', 'PP-OCRv5_mobile_det_infer')
rec_dir = os.path.join(resource_path, 'ocr', 'PP-OCRv5_mobile_rec_infer')
ori_dir = os.path.join(resource_path, 'ocr', 'PP-LCNet_x0_25_textline_ori_infer')

ocr = PaddleOCR(
    lang="ch",
    text_detection_model_name="PP-OCRv5_mobile_det",
    text_recognition_model_name="PP-OCRv5_mobile_rec",
    textline_orientation_model_name="PP-LCNet_x0_25_textline_ori",
    text_detection_model_dir=det_dir,
    text_recognition_model_dir=rec_dir,
    textline_orientation_model_dir=ori_dir,
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
)

class WindowCaptureTest:
    def __init__(self, window_name='Elsword'):
        # DPI awareness
        
        self.lock = threading.Lock()
        self.window_name = window_name
        self.w = 1920
        self.h = 1080
        self.hwnd = None
        self.cropped_x = 0
        self.cropped_y = 0
        self.offset_x = 0
        self.offset_y = 0

    def _find_window_fuzzy(self, partial_title):
        """模糊查找窗口句柄"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text and partial_title.lower() in window_text.lower():
                    windows.append((hwnd, window_text))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            logger.info(f"找到 {len(windows)} 个匹配窗口:")
            for hwnd, title in windows:
                logger.info(f"  句柄: {hwnd}, 标题: {title}")
            return windows[0][0]
        
        return None

    def _get_window_handle(self):
        """获取窗口句柄，支持精确和模糊匹配"""
        # 首先尝试精确匹配
        hwnd = win32gui.FindWindow(None, "[#] [x64] Elsword - X.250501.1_1 [#]")
        if hwnd:
            logger.debug(f"精确匹配找到窗口: {hwnd}")
            return hwnd
        
        # 精确匹配失败，尝试模糊匹配
        logger.warning(f"精确匹配失败，尝试模糊匹配: {self.window_name}")
        hwnd = self._find_window_fuzzy(self.window_name)
        if hwnd:
            logger.info(f"模糊匹配找到窗口: {hwnd}")
            return hwnd
        
        return None

    def _update_rect(self):
        # 使用增强的窗口查找方法，支持模糊匹配
        self.hwnd = self._get_window_handle()
        if not self.hwnd:
            logger.error("未找到目标窗口")
            return False

        # 获取窗口的完整矩形区域和客户区矩形区域
        window_rect = win32gui.GetWindowRect(self.hwnd)
        client_rect = win32gui.GetClientRect(self.hwnd)

        # 计算完整窗口的实际像素尺寸
        window_w = window_rect[2] - window_rect[0]
        window_h = window_rect[3] - window_rect[1]

        # 计算窗口边框和标题栏的像素大小
        border_pixels = math.floor((window_w - client_rect[2]) / 2)
        titlebar_pixels = window_h - client_rect[3] - border_pixels

        # 设置实际截图区域的尺寸（去除边框和标题栏）
        self.w = window_w - (border_pixels * 2)
        self.h = window_h - titlebar_pixels - border_pixels
        self.cropped_x = border_pixels
        self.cropped_y = titlebar_pixels

        # 设置坐标偏移量
        self.offset_x = window_rect[0] + self.cropped_x
        self.offset_y = window_rect[1] + self.cropped_y
        
        logger.info(f"窗口尺寸: {self.w}x{self.h}")
        return True

    def get_screenshot_alpha(self):
        """获取窗口截图"""
        try:
            with self.lock:
                wDC = win32gui.GetWindowDC(self.hwnd)
                dcObj = win32ui.CreateDCFromHandle(wDC)
                cDC = dcObj.CreateCompatibleDC()
                assert self.w > 0 and self.h > 0, f"Invalid dimensions: width={self.w}, height={self.h}"

                dataBitMap = win32ui.CreateBitmap()
                dataBitMap.CreateCompatibleBitmap(dcObj, self.w, self.h)
                cDC.SelectObject(dataBitMap)
                result = cDC.BitBlt((0, 0), (self.w, self.h), dcObj, (self.cropped_x, self.cropped_y), win32con.SRCCOPY)
                assert result != 0, "BitBlt failed, check dimensions and coordinates"
                
                signedIntsArray = dataBitMap.GetBitmapBits(True)
                img = np.frombuffer(signedIntsArray, dtype='uint8')
                img.shape = (self.h, self.w, 4)

                # free resources
                dcObj.DeleteDC()
                cDC.DeleteDC()
                win32gui.ReleaseDC(self.hwnd, wDC)
                win32gui.DeleteObject(dataBitMap.GetHandle())
                return img
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return None

def test_ocr(image):
    """测试OCR识别功能"""
    if image is None:
        print("图像为空，无法进行OCR")
        return False
        
    print("开始OCR识别...")
    result = ocr.predict(image)
    
    print("识别结果类型:", type(result))
    print("识别结果长度:", len(result))
    print("识别结果:", result)
    
    if isinstance(result, list) and len(result) > 0:
        print("第一个元素类型:", type(result[0]))
        print("第一个元素:", result[0])
    
    print("OCR测试完成")
    return True

if __name__ == "__main__":
    # 创建窗口捕获测试实例
    capture_test = WindowCaptureTest()
    
    # 更新窗口矩形信息
    if capture_test._update_rect():
        # 获取截图
        screenshot = capture_test.get_screenshot_alpha()
        
        if screenshot is not None:
            # 转换为BGR格式显示
            screenshot_bgr = cv2.cvtColor(screenshot, cv2.COLOR_BGRA2BGR)
            
            # 显示截图
            cv2.imshow('Window Screenshot', screenshot_bgr)
            
            # 保存截图
            cv2.imwrite('window_capture_test.png', screenshot_bgr)
            print("截图已保存为 window_capture_test.png")
            print("按任意键关闭窗口...")
            
            # 按任意键关闭
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            print("程序结束")
        else:
            print("截图失败")
    else:
        print("窗口初始化失败")
