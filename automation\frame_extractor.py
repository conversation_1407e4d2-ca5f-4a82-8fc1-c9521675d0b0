import os
import cv2

def extract_frames(video_path, output_folder, fps=1, similarity_threshold=0.7):
    """
    提取视频帧并筛选相似帧

    Args:
        video_path: 视频路径
        output_folder: 输出图片文件夹路径
        fps: 每秒提取的帧数
        similarity_threshold: 相似度阈值
    """

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise IOError("无法打开视频文件： " + video_path)

    frame_count = 0
    previous_frame = None

    # 检查输出文件夹是否存在，如果不存在则创建
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹：{output_folder}")

    while True:
        ret, frame = cap.read()
        if not ret:
            print("无法读取视频帧，可能已经到达视频末尾")
            break

        # 每秒提取fps帧
        if frame_count % int(cap.get(cv2.CAP_PROP_FPS) / fps) == 0:
            # 灰度化
            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 第一次读取帧，直接保存
            if previous_frame is None:
                previous_frame = gray_frame
                filename = f"{output_folder}/{video_name}_frame_{frame_count:04d}.jpg"
                result = cv2.imwrite(filename, frame)
                print(f"保存图片：{filename}，保存结果：{result}")
            else:
                # 计算当前帧与上一帧的相似度
                similarity = cv2.matchTemplate(gray_frame, previous_frame, cv2.TM_CCOEFF_NORMED)[0, 0]
                print(f"当前帧：{frame_count}, 相似度：{similarity:.4f}")

                # 相似度超过阈值则不保存
                if similarity < similarity_threshold:
                    filename = f"{output_folder}/{video_name}_frame_{frame_count:04d}.jpg"
                    result = cv2.imwrite(filename, frame)
                    print(f"保存图片：{filename}，保存结果：{result}")
                    previous_frame = gray_frame

        frame_count += 1

    cap.release()
    print("处理完成！总共提取的帧数:", frame_count)



video_name = '2024-10-15'

# 示例使用
video_path = rf"F:\Program_code\EIAUTO\data\{video_name}.mp4"  # 替换为您的视频路径
output_folder = rf"F:\Program_code\EIAUTO\data\{video_name}_frames"  # 替换为您的输出文件夹路径
print(f"视频路径：{video_path}")  # 打印视频路径
print(f"输出文件夹路径：{output_folder}")  # 打印输出文件夹路径
extract_frames(video_path, output_folder, fps=1, similarity_threshold=0.7)
