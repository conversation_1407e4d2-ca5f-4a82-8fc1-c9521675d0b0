import subprocess
import time
import os
import math
from mylogger.MyLogger import <PERSON><PERSON>ogger
from utils.config_manager import config_manager
import win32gui
from automation.controller.OCRController import OCRController
from automation.capture.identify_sandboxie_capture import IdentifySandboxieCapture

# 创建模块级别的日志实例
logger = MyLogger("SandboxManager", save_log=True)

class WindowsNotFoundException(Exception):
    def __init__(self, windows_name):
        super(WindowsNotFoundException, self).__init__(f"没有找到名称为'{windows_name}'的窗口!")

class SandboxManager:
    """沙盒管理器，负责通过SandMan.exe自动化创建和管理沙盒"""

    # properties
    w = 1920
    h = 1080
    hwnd = None
    cropped_x = 0
    cropped_y = 0
    offset_x = 0
    offset_y = 0
    window_name="Sandboxie-Plus"
    
    def __init__(self):
        self.settings = config_manager.get("settings")
        self.sandman_process = None
        
    def _get_sandman_path(self):
        """获取SandMan.exe路径"""
        sandboxie_path = self.settings.get('sandboxie_executable_path')
        if sandboxie_path:
            sandman_path = sandboxie_path.replace('Start.exe', 'SandMan.exe')
            if os.path.exists(sandman_path):
                return sandman_path
        return None
    
    def _start_sandman(self):
        """启动SandMan.exe"""
        try:
            sandman_path = self._get_sandman_path()
            if not sandman_path:
                logger.error("未找到SandMan.exe")
                return False
                
            self.sandman_process = subprocess.Popen([sandman_path])
            time.sleep(3)  # 等待应用启动
            logger.debug("SandMan.exe已启动")
            return True
        except Exception as e:
            logger.error(f"启动SandMan.exe失败: {e}")
            return False

    def _close_sandman(self):
        """关闭SandMan.exe"""
        try:
            if self.sandman_process:
                self.sandman_process.terminate()
                logger.debug("SandMan.exe已关闭")
        except Exception as e:
            logger.error(f"关闭SandMan.exe失败: {e}")

    def _find_window_fuzzy(self, partial_title):
        """模糊查找窗口句柄"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text and partial_title.lower() in window_text.lower():
                    windows.append((hwnd, window_text))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # 记录找到的所有匹配窗口
            logger.debug(f"找到 {len(windows)} 个匹配窗口:")
            for hwnd, title in windows:
                logger.debug(f"  句柄: {hwnd}, 标题: {title}")
            
            # 返回第一个匹配的窗口
            return windows[0][0]
        
        return None

    def _get_window_handle(self):
        """获取窗口句柄，支持精确和模糊匹配"""
        # 首先尝试精确匹配
        hwnd = win32gui.FindWindow(None, self.window_name)
        if hwnd:
            logger.debug(f"精确匹配找到窗口: {hwnd}")
            return hwnd
        
        # 精确匹配失败，尝试模糊匹配
        logger.warning(f"精确匹配失败，尝试模糊匹配: {self.window_name}")
        hwnd = self._find_window_fuzzy("sandboxie")
        if hwnd:
            logger.debug(f"模糊匹配找到窗口: {hwnd}")
            return hwnd
        
        # 都失败了，列出所有可能的窗口
        self._list_all_windows()
        return None

    def _list_all_windows(self):
        """列出所有可见窗口，用于调试"""
        def enum_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if window_text:  # 只显示有标题的窗口
                    windows.append(f"句柄: {hwnd}, 标题: {window_text}")
            return True
        
        windows = []
        win32gui.EnumWindows(enum_callback, windows)
        
        logger.debug("当前所有可见窗口:")
        for window in windows[:20]:  # 只显示前20个
            logger.debug(f"  {window}")


    def create_sandbox(self):
        """使用OCR识别并点击创建新沙盒"""
        try:
            # 1. 启动SandMan.exe
            if not self._start_sandman():
                return False
            
            time.sleep(3)
       
            sandboxie_capture = IdentifySandboxieCapture()
            ocr_controller = OCRController(gc=sandboxie_capture)
            
            # 3. 使用OCR查找并点击"沙盒"文字
            if ocr_controller.find_text_and_bg_click("沙箱", match_all=False, mss_mode=False):
                time.sleep(2)  # 等待界面响应
                
                return True
            else:
                logger.error("未找到'沙箱'按钮")
                return False
            
        except Exception as e:
            logger.error(f"创建沙盒失败: {e}")
            return False
        finally:
            self._close_sandman()

    def initialize_sandbox(self, sandbox_name):
        """初始化单个沙盒（检查存在性，不存在则创建）"""
        logger.info(f"开始初始化沙盒: {sandbox_name}")
        return self.create_sandbox()

    def initialize_all_sandboxes(self, max_count=9):
        """初始化所有沙盒（Elsrift_1 到 Elsrift_9）"""
        try:
            logger.info("开始批量初始化沙盒...")
            success_count = 0
            
            for i in range(1, max_count + 1):
                sandbox_name = f"Elsrift_{i}"
                logger.info(f"正在初始化沙盒: {sandbox_name}")
                
                if self.initialize_sandbox(sandbox_name):
                    success_count += 1
                    logger.info(f"沙盒 {sandbox_name} 初始化成功")
                else:
                    logger.error(f"沙盒 {sandbox_name} 初始化失败")
            
            logger.info(f"沙盒初始化完成，成功: {success_count}/{max_count}")
            return success_count == max_count
            
        except Exception as e:
            logger.error(f"批量初始化沙盒失败: {e}")
            return False

    def initialize_sandbox_and_send_x(self, sandbox_name):
        """初始化单个沙盒（检查存在性，不存在则创建）并发送X键"""
        logger.info(f"开始初始化沙盒: {sandbox_name}")
        return self.create_sandbox_and_send_x(sandbox_name)

    def initialize_all_sandboxes_and_send_x(self, max_count=9):
        """初始化所有沙盒（Elsrift_1 到 Elsrift_9）并发送X键"""
        try:
            logger.info("开始批量初始化沙盒并发送X键...")
            success_count = 0
            
            for i in range(1, max_count + 1):
                sandbox_name = f"Elsrift_{i}"
                logger.info(f"正在初始化沙盒: {sandbox_name}")
                
                if self.initialize_sandbox_and_send_x(sandbox_name):
                    success_count += 1
                    logger.info(f"沙盒 {sandbox_name} 初始化成功并发送了X键")
                else:
                    logger.error(f"沙盒 {sandbox_name} 初始化失败")
            
            logger.info(f"沙盒初始化完成，成功: {success_count}/{max_count}")
            return success_count == max_count
            
        except Exception as e:
            logger.error(f"批量初始化沙盒并发送X键失败: {e}")
            return False

    def _capture_and_draw_clicks(self, hwnd, click_points):
        """截图窗口并绘制点击位置"""
        try:
            import cv2
            import numpy as np
            from PIL import ImageGrab
            
            # 获取窗口位置和客户区信息
            window_rect = win32gui.GetWindowRect(hwnd)  # 整个窗口坐标
            client_rect = win32gui.GetClientRect(hwnd)  # 客户区坐标
            
            # 计算标题栏和边框大小
            window_w = window_rect[2] - window_rect[0]
            window_h = window_rect[3] - window_rect[1]
            client_w = client_rect[2]
            client_h = client_rect[3]
            
            border_w = (window_w - client_w) // 2
            titlebar_h = window_h - client_h - border_w
            
            logger.info(f"窗口信息 - 边框宽度: {border_w}, 标题栏高度: {titlebar_h}")
            logger.info(f"客户区大小: {client_w}x{client_h}")
            
            # 截图整个窗口
            screenshot = ImageGrab.grab(bbox=window_rect)
            
            # 转换为OpenCV格式
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            # 获取图像尺寸
            height, width = img_bgr.shape[:2]
            
            # 绘制窗口边界
            cv2.rectangle(img_bgr, (border_w, titlebar_h), 
                         (border_w + client_w, titlebar_h + client_h), (255, 255, 0), 2)
            cv2.putText(img_bgr, 'Client Area', (border_w + 5, titlebar_h + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # 绘制客户区坐标系（从客户区左上角开始）
            client_x = border_w
            client_y = titlebar_h
            
            # X轴 (水平线)
            cv2.line(img_bgr, (client_x, client_y), (client_x + client_w, client_y), (0, 255, 0), 2)
            # Y轴 (垂直线)
            cv2.line(img_bgr, (client_x, client_y), (client_x, client_y + client_h), (0, 255, 0), 2)
            
            # 绘制坐标刻度（客户区坐标系）
            for x in range(0, client_w, 50):
                screen_x = client_x + x
                cv2.line(img_bgr, (screen_x, client_y), (screen_x, client_y + 10), (0, 255, 0), 1)
                cv2.putText(img_bgr, str(x), (screen_x - 10, client_y + 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            
            for y in range(0, client_h, 50):
                screen_y = client_y + y
                cv2.line(img_bgr, (client_x, screen_y), (client_x + 10, screen_y), (0, 255, 0), 1)
                cv2.putText(img_bgr, str(y), (client_x + 15, screen_y + 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            
            # 在图像上绘制点击位置（转换为屏幕坐标）
            for i, (x, y) in enumerate(click_points):
                # 转换客户区坐标到屏幕坐标
                screen_x = client_x + x
                screen_y = client_y + y
                
                # 绘制红色圆圈标记点击位置
                cv2.circle(img_bgr, (screen_x, screen_y), 10, (0, 0, 255), 2)
                
                # 绘制十字线到坐标轴
                cv2.line(img_bgr, (screen_x, client_y), (screen_x, screen_y), (255, 0, 0), 1)
                cv2.line(img_bgr, (client_x, screen_y), (screen_x, screen_y), (255, 0, 0), 1)
                
                # 添加文字标签
                cv2.putText(img_bgr, f'Click {i+1}: ({x},{y})', 
                           (screen_x + 15, screen_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 
                           0.6, (0, 0, 255), 2)
                
                # 在坐标轴上标记坐标值
                cv2.putText(img_bgr, f'{x}', (screen_x - 10, client_y + 40), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                cv2.putText(img_bgr, f'{y}', (client_x + 25, screen_y + 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
            
            # 添加说明文字
            cv2.putText(img_bgr, 'Client Area Coordinates - Press any key to continue...', 
                       (10, height-20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 显示图像并等待用户手动关闭
            cv2.imshow('Sandbox Window - Click Positions with Client Coordinates', img_bgr)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            
            # 保存截图到文件
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"sandbox_clicks_{timestamp}.png"
            cv2.imwrite(filename, img_bgr)
            logger.info(f"截图已保存: {filename}")
            
        except Exception as e:
            logger.error(f"截图绘制失败: {e}")
