<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UpdateDialog</class>
 <widget class="QDialog" name="UpdateDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>408</width>
    <height>559</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">/* 全局样式 */
QDialog {
    background-color: #121317; /* 深蓝灰背景 */
    color: #e0e0e0;            /* 浅灰色文字 */
    font-family: &quot;Microsoft YaHei&quot;, &quot;微软雅黑&quot;, sans-serif;
}

#MainForm {
    border-radius: 12px; /* 圆角 */
}</string>
  </property>
  <widget class="QPushButton" name="btn_update">
   <property name="geometry">
    <rect>
     <x>212</x>
     <y>480</y>
     <width>156</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>-1</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>PointingHandCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">#btn_update {
    color: #FFFFFF;
    background-color: #2B9D7C; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 14px;            /* 字体大小 */
}

#btn_update:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
   </property>
   <property name="text">
    <string>保存</string>
   </property>
  </widget>
  <widget class="QPushButton" name="btn_back">
   <property name="geometry">
    <rect>
     <x>32</x>
     <y>480</y>
     <width>156</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>-1</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="cursor">
    <cursorShape>PointingHandCursor</cursorShape>
   </property>
   <property name="styleSheet">
    <string notr="true">#btn_back {
    color: #FFFFFF;
    background-color: #21252D; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 14px;            /* 字体大小 */
}

#btn_back:hover {
        border: 1px solid #2B9D7C;
}</string>
   </property>
   <property name="text">
    <string>取消</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_username">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>60</y>
     <width>340</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#lineEdit_username {
	margin:2px;
	padding-left:8px;
    background-color: #121317; /* 深蓝灰背景 */
	color:#FFFFFF;
    border: 1px solid #8C8D90;
    border-radius: 8px; /* 圆角 */
}

#lineEdit_username:hover {
    border: 1px solid #2B9D7C;
}

#lineEdit_username:focus {
    border: 1px solid #2B9D7C;
}

</string>
   </property>
   <property name="inputMask">
    <string/>
   </property>
   <property name="placeholderText">
    <string>请输入邮箱账号(必填)</string>
   </property>
  </widget>
  <widget class="QLabel" name="laber_username">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>26</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>13</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#laber_username {
    background-color: transparent; 
	color:white；
}</string>
   </property>
   <property name="text">
    <string>📧  账号</string>
   </property>
  </widget>
  <widget class="QLabel" name="laber_password">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>116</y>
     <width>81</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>13</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#laber_password {
    background-color: transparent; 
	color:white；
}</string>
   </property>
   <property name="text">
    <string>🔑  密码</string>
   </property>
  </widget>
  <widget class="QLabel" name="laber_name">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>292</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>13</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#laber_name {
    background-color: transparent; 
	color:white；
}</string>
   </property>
   <property name="text">
    <string>🎮  游戏名</string>
   </property>
  </widget>
  <widget class="QLabel" name="laber_role">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>382</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>13</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#laber_role {
    background-color: transparent; 
	color:white；
}</string>
   </property>
   <property name="text">
    <string>🤖  角色</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_password">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>150</y>
     <width>340</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#lineEdit_password {
	margin:2px;
	padding-left:8px;
    background-color: #121317; /* 深蓝灰背景 */
	color:#FFFFFF;
    border: 1px solid #8C8D90;
    border-radius: 8px; /* 圆角 */
}

#lineEdit_password:hover {
    border: 1px solid #2B9D7C;
}

#lineEdit_password:focus {
    border: 1px solid #2B9D7C;
}

</string>
   </property>
   <property name="inputMask">
    <string/>
   </property>
   <property name="placeholderText">
    <string>请输入密码(必填)</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_name">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>326</y>
     <width>340</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#lineEdit_name {
	margin:2px;
	padding-left:8px;
    background-color: #121317; /* 深蓝灰背景 */
	color:#FFFFFF;
    border: 1px solid #8C8D90;
    border-radius: 8px; /* 圆角 */
}

#lineEdit_name:hover {
    border: 1px solid #2B9D7C;
}

#lineEdit_name:focus {
    border: 1px solid #2B9D7C;
}

</string>
   </property>
   <property name="inputMask">
    <string/>
   </property>
   <property name="placeholderText">
    <string>请输入游戏角色名(必填)</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_role">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>416</y>
     <width>340</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#lineEdit_role {
	margin:2px;
	padding-left:8px;
    background-color: #121317; /* 深蓝灰背景 */
	color:#FFFFFF;
    border: 1px solid #8C8D90;
    border-radius: 8px; /* 圆角 */
}

#lineEdit_role:hover {
    border: 1px solid #2B9D7C;
}

#lineEdit_role:focus {
    border: 1px solid #2B9D7C;
}

</string>
   </property>
   <property name="inputMask">
    <string/>
   </property>
   <property name="placeholderText">
    <string>请输入角色</string>
   </property>
  </widget>
  <widget class="QLabel" name="laber_pin">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>202</y>
     <width>91</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>13</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#laber_pin {
    background-color: transparent; 
	color:white；
}</string>
   </property>
   <property name="text">
    <string>🔑  PIN码</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_pin">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>236</y>
     <width>340</width>
     <height>40</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">#lineEdit_pin {
	margin:2px;
	padding-left:8px;
    background-color: #121317; /* 深蓝灰背景 */
	color:#FFFFFF;
    border: 1px solid #8C8D90;
    border-radius: 8px; /* 圆角 */
}

#lineEdit_pin:hover {
    border: 1px solid #2B9D7C;
}

#lineEdit_pin:focus {
    border: 1px solid #2B9D7C;
}

</string>
   </property>
   <property name="inputMask">
    <string/>
   </property>
   <property name="placeholderText">
    <string>请输入PIN码(必填)</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
