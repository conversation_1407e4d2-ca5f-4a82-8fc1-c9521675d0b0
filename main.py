from utils.config_manager import config_manager
from mylogger.MyLogger import MyLogger
import sys
import os
import platform
from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtGui import QIcon
from PySide6.QtCore import Qt
from gui.uis.windows.ui_main import Ui_MainForm
from gui.controllers.main_controller import MainController
from gui.core.functions import Functions
from gui.components.toast_manager import init_toast_manager

# 创建主程序日志实例
logger = MyLogger("main", save_log=True)

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        
        # 使用 .ico 文件作为图标
        app_path = os.path.abspath(os.getcwd())
        icon_path = os.path.join(app_path, "gui/images/svg_icons/icon_logo.ico")
        self.setWindowIcon(QIcon(icon_path))
        
        # 记录图标设置日志
        logger.info(f"应用程序图标已设置为: {icon_path}")
        
        # 隐藏系统默认的标题栏和窗口控制按钮
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        
        self.ui = Ui_MainForm()
        self.ui.setupUi(self)
        
        # 初始化全局Toast管理器
        init_toast_manager(self)
        
        # 初始化主控制器
        self.main_controller = MainController(self.ui, self)
    
    # 将鼠标事件委托给主控制器
    def mousePressEvent(self, event):
        self.main_controller.mousePressEvent(event)

    def mouseMoveEvent(self, event):
        self.main_controller.mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        self.main_controller.mouseReleaseEvent(event)

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            logger.info(f"{config_manager.get('settings', 'app_name', 'BetterElsrift')}正在关闭...")
            
            # 异步清理主控制器资源，避免阻塞UI
            if hasattr(self, 'main_controller'):
                # 使用QTimer延迟执行清理，让UI先响应关闭
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, self.main_controller.cleanup)
            
            event.accept()  # 立即接受关闭事件
            
        except Exception as e:
            logger.error(f"关闭窗口时出错: {e}")
            event.accept()  # 即使出错也要关闭

def main():
    app = QApplication(sys.argv)
    
    # 记录启动日志
    app_name = config_manager.get("settings", "app_name", "BetterElsrift")
    logger.info(f"{app_name}已启动")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"平台信息: {platform.system()}")
    
    window = MainWindow()
    window.show()
    
    logger.info("主窗口已显示")
    
    try:
        sys.exit(app.exec())
    except SystemExit:
        logger.info(f"{app_name}已关闭")

if __name__ == "__main__":
    main()

