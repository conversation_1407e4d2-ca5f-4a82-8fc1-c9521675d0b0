import winreg
import os
from mylogger.MyLogger import MyLogger

# 创建模块级别的日志实例
logger = MyLogger("SandboxieDetector", save_log=True)

class SandboxieDetector:
    @staticmethod
    def get_sandboxie_info():
        """通过注册表获取Sandboxie-Plus的安装信息"""
        registry_paths = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
        ]
        
        for hkey, path in registry_paths:
            try:
                with winreg.OpenKey(hkey, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.<PERSON>um<PERSON><PERSON>(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if "sandboxie" in display_name.lower():
                                        # 获取版本号
                                        try:
                                            version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                        except FileNotFoundError:
                                            version = "未知版本"
                                        
                                        # 获取安装路径
                                        try:
                                            install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                        except FileNotFoundError:
                                            install_location = None
                                        
                                        # 查找可执行文件路径
                                        executable_path = None
                                        if install_location:
                                            exe_files = ['Start.exe', 'SandMan.exe', 'SbieCtrl.exe']
                                            for exe in exe_files:
                                                exe_path = os.path.join(install_location, exe)
                                                if os.path.exists(exe_path):
                                                    executable_path = exe_path
                                                    break
                                        
                                        logger.info(f"检测到Sandboxie: {display_name} {version}")
                                        return {
                                            'name': display_name,
                                            'version': version,
                                            'install_location': install_location,
                                            'executable_path': executable_path,
                                            'detected': True
                                        }
                                except FileNotFoundError:
                                    pass
                        except OSError:
                            continue
            except FileNotFoundError:
                continue
        
        logger.warning("未检测到Sandboxie-Plus安装")
        return {'detected': False}

