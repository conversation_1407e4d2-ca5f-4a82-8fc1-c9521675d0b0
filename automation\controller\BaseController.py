import time
from pynput import mouse
from pynput.mouse import But<PERSON>
from pynput.keyboard import Key, Controller
from pynput import keyboard
import win32gui
import win32api, win32con
from automation.capture.elsword_capture import ElswordCapture
from mylogger.MyLogger import MyLogger
from automation.myutils.configutils import DebugConfig
import logging

logger = MyLogger('BaseController')

class StopListenException(Exception): pass

def wait_for_window(capture_obj):
    if BaseController.stop_listen:
        logger.debug('停止监听')
        raise StopListenException('停止监听')

    while not capture_obj.is_active():
        if BaseController.stop_listen:
            logger.debug('停止监听')
            raise StopListenException('停止监听')
        logger.debug('窗口未激活，暂停运行')
        time.sleep(1)

class KeyBoardController(keyboard.Controller):
    def __init__(self, handler):
        super().__init__()
        self.handler = handler

    def press(self, key):
        wait_for_window()
        super().press(key)

    def release(self, key):
        wait_for_window()
        super().release(key)

class MouseController(mouse.Controller):
    def __init__(self):
        super().__init__()

    def scroll(self, dx, dy):
        wait_for_window()
        super().scroll(dx, dy)

    def press(self, button):
        wait_for_window()
        super().press(button)

    def release(self, button):
        wait_for_window()
        super().release(button)

    def move(self, x, y):
        wait_for_window()
        super().move(x, y)

    def click(self, button, count=1):
        wait_for_window()
        super().click(button, count)

    @property
    def position(self):
        return super().position

    @position.setter
    def position(self, pos):
        wait_for_window()
        mouse.Controller.position.fset(self, pos)

class BaseController:

    def log(self, *args):
        if self.debug_enable:
            self.logger.debug(args)

    stop_listen = False

    def __init__(self,debug_enable=None, gc=None):
        self.Key = Key
        
        # 如果没有传入capture对象，创建新的工厂实例
        if gc is None:
            self.gc = ElswordCapture()
        else:
            self.gc = gc

        if debug_enable is None:
            debug_enable = DebugConfig.get(DebugConfig.KEY_DEBUG_ENABLE, False)
            if debug_enable: self.logger = MyLogger(self.__class__.__name__, logging.DEBUG)
            else: self.logger = MyLogger(self.__class__.__name__, logging.INFO)
        else:
            self.logger = MyLogger(self.__class__.__name__, logging.DEBUG)

        self.Button = Button
        self.__keyboard = keyboard.Controller()  # 修复：添加括号实例化
        self.__ms = mouse.Controller()           # 修复：添加括号实例化

        self.debug_enable = debug_enable

        # 后台操作支持
        self.hwnd = None
        self._init_background_support()
    
    def _init_background_support(self):
        """初始化后台操作支持"""
        try:
            if hasattr(self.gc, 'hwnd') and self.gc.hwnd:
                self.hwnd = self.gc.hwnd
            else:
                window_name = getattr(self.gc, 'window_name', 'Elsword')
                self.hwnd = win32gui.FindWindow(None, window_name)
        except Exception as e:
            logger.warning(f"后台操作初始化失败: {e}")
    
    # === 后台操作方法（win32api） ===
    def bg_send_key(self, key_code):
        """后台发送按键"""
        if not self.hwnd:
            logger.error("窗口句柄未找到，无法后台操作")
            return False
        try:
            win32api.SendMessage(self.hwnd, win32con.WM_KEYDOWN, key_code, 0)
            win32api.SendMessage(self.hwnd, win32con.WM_KEYUP, key_code, 0)
            return True
        except Exception as e:
            logger.error(f"后台按键失败: {e}")
            return False
    
    def bg_click(self, x, y, button='left'):
        """后台点击窗口内坐标"""
        if not self.hwnd:
            logger.error("窗口句柄未找到，无法后台操作")
            return False

        # TODO: 显示点击位置图片
        self._show_click_position(x, y)
        
        try:
            # 确保坐标为整数
            x, y = int(x), int(y)
            lParam = win32api.MAKELONG(x, y)
            if button == 'left':
                win32api.SendMessage(self.hwnd, win32con.WM_LBUTTONDOWN, 0, lParam)
                win32api.SendMessage(self.hwnd, win32con.WM_LBUTTONUP, 0, lParam)
            elif button == 'right':
                win32api.SendMessage(self.hwnd, win32con.WM_RBUTTONDOWN, 0, lParam)
                win32api.SendMessage(self.hwnd, win32con.WM_RBUTTONUP, 0, lParam)
            return True
        except Exception as e:
            logger.error(f"后台点击失败: {e}")
            return False
    
    def _show_click_position(self, x, y):
        """显示点击位置的可视化"""
        try:
            import cv2
            import numpy as np
            # 获取当前窗口截图
            screenshot = self.gc.get_screenshot()
            if screenshot is None:
                return
            
            # 复制截图以避免只读错误
            screenshot1 = screenshot.copy()
            
            # 在截图上绘制点击位置
            cv2.circle(screenshot1, (int(x), int(y)), 10, (0, 0, 255), 2)
            cv2.circle(screenshot1, (int(x), int(y)), 3, (0, 255, 0), -1)
            
            # 添加坐标文字
            cv2.putText(screenshot1, f'Click: ({x},{y})', 
                       (int(x) + 15, int(y) - 10), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 0, 255), 2)
            
            # 显示图像
            cv2.imshow('Click Position Preview', screenshot1)
            cv2.waitKey(10000)  # 显示1秒
            cv2.destroyWindow('Click Position Preview')
            
        except Exception as e:
            logger.warning(f"显示点击位置失败: {e}")
    
    def bg_click_screen(self, pos, button='left'):
        """后台点击屏幕坐标"""
        return self.bg_click(pos[0], pos[1], button)
    
    def bg_press_and_release(self, key_code):
        """后台按键（按下并释放）"""
        return self.bg_send_key(key_code)
    
    def bg_type_string(self, text):
        """后台输入字符串"""
        if not self.hwnd:
            return False
        try:
            for char in text:
                win32api.SendMessage(self.hwnd, win32con.WM_CHAR, ord(char), 0)
                time.sleep(0.05)
            return True
        except Exception as e:
            logger.error(f"后台输入失败: {e}")
            return False
    
    def set_target_window(self, hwnd):
        """设置目标窗口句柄"""
        self.hwnd = hwnd
        
    def get_target_window(self):
        """获取当前目标窗口句柄"""
        return self.hwnd

    # === 前台操作方法（pynput） ===
    def set_ms_position(self, pos):
        self.__ms.position = pos

    def get_ms_position(self):
        return self.__ms.position

    def ms_scroll(self, dx, dy):
        self.__ms.scroll(dx, dy)

    def ms_click(self, button):
        self.__ms.click(button)

    def mouse_left_click(self):
        self.ms_click(self.Button.left)

    def mouse_right_click(self):
        self.ms_click(self.Button.right)

    def click_screen(self, pos, button: Button = Button.left):
        """前台点击游戏内坐标"""
        sc_pos = self.gc.get_screen_position(pos)
        self.set_ms_position(sc_pos)
        self.ms_click(button)

    def kb_press_and_release(self, key):
        self.kb_press(key)
        self.kb_release(key)

    def kb_press(self, key):
        """按下键盘"""
        self.__keyboard.press(key)

    def kb_release(self, key):
        self.__keyboard.release(key)

    def kb_paste(self):
        """模拟粘贴"""
        self.kb_press(self.Key.ctrl)
        self.kb_press("v")
        self.kb_release("v")
        self.kb_release(self.Key.ctrl)

    # def _on_press(self, key):
    #     try:
    #         c = key.char
    #     except AttributeError:
    #         # print('special key {0} pressed'.format(key))
    #         if key == Key.esc:
    #             self.log('你按下了esc退出程序')
    #             self.stop_listen = True
    #             self.ms_listener.stop()
    #             self.kb_listener.stop()
    #             sys.exit(0)

    # def _on_release(self, key):
    #     pass

    def drag(self, position_from, dx, dy, duration_ms=200):
        """
        鼠标拖动
        :param position_from: 起始位置
        :param dx: 水平距离
        :param dy: 垂直距离
        :param duration_ms:持续时间（毫秒）
        :return:
        """
        self.__ms.position = position_from
        x, y = position_from
        finalx, finaly = x + dx, y + dy
        self.ms_press(Button.left)
        move_times = 5  # 移动次数(不要设高了，避免漂移)
        gap_time = (duration_ms / move_times) * 0.001  # 移动次数除以间隔
        gap_x = dx / move_times
        gap_y = dy / move_times
        while move_times > 0:
            time.sleep(gap_time)
            x, y = self.get_ms_position()
            self.set_ms_position((x + gap_x, y + gap_y))
            move_times -= 1
        self.set_ms_position((finalx, finaly))  # 确保鼠标在最终位置
        self.ms_release(Button.left)

    def zoom_out(self, delta=5):
        """缩小（前台操作）"""
        for _ in range(abs(int(delta))):
            self.ms_scroll(0, -1)
    def zoom_in(self, delta=5):
        """放大（前台操作）"""
        for _ in range(abs(int(delta))):
            self.ms_scroll(0, 1)

    def ms_press(self, button):
        self.__ms.press(button)

    def ms_release(self, button):
        wait_for_window(self.gc)  # 修复：传入capture对象
        self.__ms.release(button)

    # === 游戏特定操作方法（前台win32api） ===
    def camera_chage(self, dx: int, dy: int, scroll: int = 0):
        """相机视角改变（前台操作）"""
        wait_for_window()
        win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, int(dx), int(dy), int(scroll), 0)

    def view_reset(self):
        """恢复视角（前台操作）"""
        self.ms_middle_press()
        self.ms_middle_release()

    def view_down(self):
        """视角拉到最下（前台操作）"""
        win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, 0, 20000, 0, 0)
        time.sleep(0.02)
        win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, 0, 20000, 0, 0)



if __name__ == '__main__':
    bc = BaseController()

    # bc.crazy_f()

        # win32api.mouse_event(win32con.MOUSEEVENTF_WHEEL, 0, 0, 120, 0)
    # bc.crazy_f()

    # bc.click_if_appear(capture.icon_message_box_button_confirm)
    # time.sleep(0.5)
    # bc.click_if_appear(capture.icon_map_setting_on)
    # time.sleep(0.4)
    # bc.click_if_appear(capture.icon_close_while_arrow)
    # time.sleep(0.4)
    # bc.click_if_appear(capture.icon_close_tob_bar)

    # for i in range(1,10):
        # 稍微动一下屏幕让模板匹配更容易成功
        # x = randint(-500,500)
        # y = randint(-500,500)
        # bc.camera_chage(x,y)
        # time.sleep(0.2)

        # bc.ms_middle_press()
        # time.sleep(1)
        # if i%2 == 0:
        #     bc.to_degree(-170)
        # else:
        #     bc.to_degree(170)
    #     time.sleep(2)

    # Logger.log("good", instance=BaseController)
    # bc.drag(GenShinCapture.get_genshin_screen_center(),1000, 200, 500)
    # bc.ms.position = (3858.0, 2322.0)
    # bc.ms.click(Button.left)














