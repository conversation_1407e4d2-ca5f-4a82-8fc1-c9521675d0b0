from pathlib import Path
import yaml
import json
import os

class ConfigService:
    """统一配置服务，作为所有配置的访问点"""
    
    _instance = None
    
    def __new__(cls):
        # 单例模式
        if cls._instance is None:
            cls._instance = super(ConfigService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.app_path = os.path.abspath(os.getcwd())
        
        # 初始化各配置管理器
        self.ui_config = UIConfigManager()
        self.business_config = BusinessConfigManager()
        
        # 加载所有配置
        self.ui_config.load_config()
        self.business_config.load_all_configs()
        
        self._initialized = True
    
    def get_ui_config(self, key=None):
        """获取UI配置"""
        if key:
            return self.ui_config.get(key)
        return self.ui_config.config
    
    def get_business_config(self, config_name, key=None):
        """获取业务配置"""
        config = self.business_config.get_config(config_name)
        if key and isinstance(config, dict):
            # 支持点号访问嵌套配置，如"gui.theme"
            parts = key.split('.')
            result = config
            for part in parts:
                if isinstance(result, dict) and part in result:
                    result = result[part]
                else:
                    return None
            return result
        return config
    
    def update_business_config(self, config_name, updates, save=True):
        """更新业务配置"""
        return self.business_config.update_config(config_name, updates, save)
    
    def update_ui_config(self, updates, save=True):
        """更新UI配置"""
        for key, value in updates.items():
            self.ui_config.update(key, value)
        
        if save:
            self.ui_config.save_config()
        
        return True
    
    # 便捷方法
    def get_account(self, account_id):
        """获取账号信息"""
        accounts = self.get_business_config("accounts")
        account_list = accounts.get("accounts", [])
        for account in account_list:
            if account.get("id") == account_id:
                return account
        return None
    
    def get_all_accounts(self):
        """获取所有账号"""
        accounts = self.get_business_config("accounts")
        return accounts.get("accounts", [])
    
    def add_account(self, account_data):
        """添加账号"""
        accounts = self.get_business_config("accounts")
        account_list = accounts.get("accounts", [])
        
        # 生成新ID
        max_id = 0
        for account in account_list:
            if account.get("id", 0) > max_id:
                max_id = account.get("id")
        
        account_data["id"] = max_id + 1
        account_list.append(account_data)
        
        return self.update_business_config("accounts", {"accounts": account_list})
    
    def update_account(self, account_id, updates):
        """更新账号"""
        accounts = self.get_business_config("accounts")
        account_list = accounts.get("accounts", [])
        
        for i, account in enumerate(account_list):
            if account.get("id") == account_id:
                account_list[i].update(updates)
                return self.update_business_config("accounts", {"accounts": account_list})
        
        return False
    
    def delete_account(self, account_id):
        """删除账号"""
        accounts = self.get_business_config("accounts")
        account_list = accounts.get("accounts", [])
        
        for i, account in enumerate(account_list):
            if account.get("id") == account_id:
                del account_list[i]
                return self.update_business_config("accounts", {"accounts": account_list})
        
        return False
    
    def get_recognition_model(self):
        """获取识别模型配置"""
        return self.get_business_config("recognition", "model")
    
    def update_recognition_model(self, updates):
        """更新识别模型配置"""
        return self.update_business_config("recognition", {"model": updates})
    
    def get_tasks(self):
        """获取所有任务"""
        tasks = self.get_business_config("tasks")
        return tasks.get("tasks", {})
    
    def get_task(self, task_id):
        """获取特定任务"""
        tasks = self.get_tasks()
        return tasks.get(task_id)


class UIConfigManager:
    """UI配置管理器"""
    
    def __init__(self):
        self.json_file = "settings.json"
        self.app_path = os.path.abspath(os.getcwd())
        self.settings_path = os.path.normpath(os.path.join(self.app_path, self.json_file))
        self.config = {}
    
    def load_config(self):
        """加载UI配置"""
        try:
            with open(self.settings_path, "r", encoding='utf-8') as reader:
                self.config = json.loads(reader.read())
            return self.config
        except Exception as e:
            print(f"加载UI配置失败: {str(e)}")
            return {}
    
    def save_config(self):
        """保存UI配置"""
        try:
            with open(self.settings_path, "w", encoding='utf-8') as writer:
                json.dump(self.config, writer, indent=4)
            return True
        except Exception as e:
            print(f"保存UI配置失败: {str(e)}")
            return False
    
    def get(self, key=None):
        """获取配置项"""
        if key is None:
            return self.config
        
        # 支持点号访问嵌套配置，如"font.family"
        parts = key.split('.')
        result = self.config
        for part in parts:
            if isinstance(result, dict) and part in result:
                result = result[part]
            else:
                return None
        return result
    
    def update(self, key, value):
        """更新配置项"""
        if '.' not in key:
            self.config[key] = value
            return True
        
        # 处理嵌套配置
        parts = key.split('.')
        config = self.config
        
        # 遍历路径直到最后一个部分
        for part in parts[:-1]:
            if part not in config:
                config[part] = {}
            elif not isinstance(config[part], dict):
                config[part] = {}
            config = config[part]
        
        # 设置最后一个部分的值
        config[parts[-1]] = value
        return True


class BusinessConfigManager:
    """业务配置管理器"""
    
    def __init__(self, config_dir="config"):
        self.config_dir = config_dir
        self.configs = {}
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 默认配置文件列表
        self.config_files = [
            "app_settings.yaml",
            "accounts.yaml",
            "tasks.yaml",
            "recognition.yaml"
        ]
    
    def load_all_configs(self):
        """加载所有配置文件"""
        for config_file in self.config_files:
            self.load_config(config_file)
    
    def load_config(self, config_file):
        """加载指定配置文件"""
        config_path = os.path.join(self.config_dir, config_file)
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                # 存储到内存中
                config_name = Path(config_file).stem
                self.configs[config_name] = config
                return config
        except Exception as e:
            print(f"加载配置文件 {config_file} 失败: {str(e)}")
            return {}
    
    def get_config(self, config_name):
        """获取指定配置"""
        return self.configs.get(config_name, {})
    
    def update_config(self, config_name, updates, save=True):
        """更新配置并可选保存"""
        if config_name not in self.configs:
            self.configs[config_name] = {}
        
        # 递归更新配置
        self._update_dict(self.configs[config_name], updates)
        
        # 保存到文件
        if save:
            config_file = f"{config_name}.yaml"
            self.save_config(config_file, self.configs[config_name])
            return True
        return False
    
    def save_config(self, config_file, config_data):
        """保存配置到文件"""
        config_path = os.path.join(self.config_dir, config_file)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            return True
        except Exception as e:
            print(f"保存配置文件 {config_file} 失败: {str(e)}")
            return False
    
    def _update_dict(self, target, source):
        """递归更新字典"""
        for key, value in source.items():
            if isinstance(value, dict) and key in target and isinstance(target[key], dict):
                self._update_dict(target[key], value)
            else:
                target[key] = value
