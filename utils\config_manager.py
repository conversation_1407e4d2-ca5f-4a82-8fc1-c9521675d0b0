import json
import os
from pathlib import Path
from mylogger.MyLogger import MyLogger

# 创建模块级别的日志实例
logger = MyLogger("ConfigManager", save_log=True)

class ConfigManager:
    """通用配置管理器，单例模式"""
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.configs = {}
        self.load_all_configs()
        self._initialized = True
    
    def load_all_configs(self):
        """加载所有配置文件"""
        # 主配置文件
        self.load_config("settings", "settings.json")
        
        # config目录下的配置文件
        config_dir = Path("config")
        if config_dir.exists():
            for config_file in config_dir.glob("*.json"):
                config_name = config_file.stem
                self.load_config(config_name, str(config_file))
    
    def load_config(self, config_name, file_path):
        """加载指定配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.configs[config_name] = json.load(f)
            logger.info(f"成功加载配置文件: {file_path}")
        except Exception as e:
            logger.error(f"加载配置文件 {file_path} 失败: {e}")
            self.configs[config_name] = {}
    
    def get(self, config_name, key=None, default=None):
        """获取配置项
        
        Args:
            config_name: 配置文件名（不含扩展名）
            key: 配置键，支持点号分隔的嵌套访问，如 "database.host"
            default: 默认值
        """
        config = self.configs.get(config_name, {})
        
        if key is None:
            return config
        
        # 支持点号访问嵌套配置
        keys = key.split('.')
        result = config
        for k in keys:
            if isinstance(result, dict) and k in result:
                result = result[k]
            else:
                return default
        
        return result
    
    def update(self, config_name, key, value):
        """更新配置项"""
        if config_name not in self.configs:
            self.configs[config_name] = {}
        
        # 支持点号设置嵌套配置
        keys = key.split('.')
        config = self.configs[config_name]
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, config_name, file_path=None):
        """保存配置到文件"""
        if config_name not in self.configs:
            return False
        
        if file_path is None:
            if config_name == "settings":
                file_path = "settings.json"
            else:
                file_path = f"config/{config_name}.json"
        
        try:
            # 确保目录存在（只有当目录路径不为空时）
            dir_path = os.path.dirname(file_path)
            if dir_path:  # 只有当目录路径不为空时才创建
                os.makedirs(dir_path, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.configs[config_name], f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"保存配置文件 {file_path} 失败: {e}")
            return False
    
    def reload_config(self, config_name):
        """重新加载指定配置"""
        if config_name == "settings":
            file_path = "settings.json"
        else:
            file_path = f"config/{config_name}.json"
        
        self.load_config(config_name, file_path)

    def update_and_save(self, config_name, data):
        """更新配置并立即保存到文件"""
        self.configs[config_name] = data
        return self.save_config(config_name)

    def get_accounts_list(self):
        """获取账户列表"""
        accounts_config = self.get("accounts", default={})
        return accounts_config.get("accounts", [])

    def get_account_settings(self):
        """获取账户设置"""
        accounts_config = self.get("accounts", default={})
        return accounts_config.get("settings", {})

    def update_accounts_data(self, data):
        """更新账户数据并保存"""
        return self.update_and_save("accounts", data)

    def reload_accounts(self):
        """重新加载账户配置"""
        self.reload_config("accounts")

    def get_account_by_id(self, account_id):
        """根据ID获取账户"""
        accounts = self.get_accounts_list()
        for account in accounts:
            if account.get("id") == account_id:
                return account
        return None

# 全局配置实例
config_manager = ConfigManager()




