import cv2
import numpy as np
from ultralytics import YOLO

# 加载模型
model = YOLO('runs/detect/train/weights/best.pt')

# 设置目标帧率
target_fps = 60  # 目标帧率（可以根据需要设置）
wait_time = int(1000 / target_fps)  # 毫秒

# 替换为你的输入视频文件路径
video_path = 'datasets/2024-10-25.mp4'  # 这里替换为你的视频文件路径

# 打开视频文件
cap = cv2.VideoCapture(video_path)

if not cap.isOpened():
    print("Error: Could not open video.")
    exit()

while True:
    # 读取视频帧
    ret, frame = cap.read()
    if not ret:
        print("End of video or error occurred.")
        break

    # 降低分辨率以提高性能
    frame_resized = cv2.resize(frame, (640, 480))  # 将帧调整为640x480分辨率

    # 进行推理
    results = model.predict(source=frame_resized, conf=0.25)  # 将置信度阈值设为0.25

    # 处理结果
    for result in results:
        boxes = result.boxes  # 获取检测框
        for box in boxes:
            # 在帧上绘制检测框
            x1, y1, x2, y2 = box.xyxy[0]  # 框的坐标
            confidence = box.conf[0]  # 置信度
            cls = int(box.cls[0])  # 类别，确保为整数

            # 绘制矩形框
            cv2.rectangle(frame_resized, (int(x1), int(y1)), (int(x2), int(y2)), (255, 0, 0), 2)
            cv2.putText(frame_resized, f'Class: {cls}, Conf: {confidence:.2f}',
                        (int(x1), int(y1) - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

    # 显示带有检测框的帧
    cv2.imshow('Video Detection', frame_resized)

    # 使用目标帧率控制播放速度
    if cv2.waitKey(wait_time) & 0xFF == ord('q'):
        break

# 释放视频捕获对象和关闭窗口
cap.release()
cv2.destroyAllWindows()
