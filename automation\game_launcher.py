import subprocess
import os
import time
from mylogger.MyLogger import MyLogger
from utils.config_manager import config_manager
from gui.components.toast_manager import show_toast


# 创建模块级别的日志实例
logger = MyLogger("GameLauncher", save_log=True)

class GameLauncher:
    """游戏启动器，负责通过Sandbox启动Elsrift"""
    
    def __init__(self):
        self.settings = config_manager.get("settings")
        self.powershell_process = None

    def _start_powershell(self):
        """启动PowerShell进程"""
        try:
            self.powershell_process = subprocess.Popen(
                ["powershell.exe", "-NoExit"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            logger.info(f"PowerShell进程已启动，PID: {self.powershell_process.pid}")
            time.sleep(2)  # 等待PowerShell启动
            logger.info("成功连接到PowerShell进程")
            return True
        except Exception as e:
            logger.error(f"启动PowerShell失败: {e}")
            return False

    def launch_game_with_sandbox(self, account_info, sandbox_index):
        """通过PowerShell执行Sandboxie启动命令"""
        try:
            sandboxie_path = self.settings.get('sandboxie_executable_path')
            elsrift_path = self.settings.get('elsrift_executable_path')
            sandbox_name = f"Elsrift_{sandbox_index}"
            
            logger.info(f"启动游戏: {account_info['username']} 在沙盒 {sandbox_name}")
            
            # 构建PowerShell命令
            powershell_cmd = f'& "{sandboxie_path}" /box:{sandbox_name} "{elsrift_path}"'
            
            # 使用subprocess执行PowerShell命令
            result = subprocess.run(
                ["powershell.exe", "-Command", powershell_cmd],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info(f"PowerShell命令执行成功: {powershell_cmd}")
                time.sleep(3)
                logger.info("游戏启动命令已执行")
                return True
            else:
                logger.error(f"PowerShell命令执行失败: {result.stderr}")
                return False
        
        except subprocess.TimeoutExpired:
            logger.error("PowerShell命令执行超时")
            return False
        except Exception as e:
            logger.error(f"启动游戏失败: {e}")
            return False

    def launch_multiple_accounts(self, selected_accounts):
        """启动多个账号"""
        success_count = 0
        
        for i, account_identifier in enumerate(selected_accounts, 1):
            # 解析账号信息
            account_info = self._parse_account_identifier(account_identifier)
            
            if account_info:
                if self.launch_game_with_sandbox(account_info, i):
                    success_count += 1
                    time.sleep(3)  # 启动间隔
                else:
                    logger.error(f"启动账号失败: {account_identifier}")
        
        logger.info(f"成功启动 {success_count}/{len(selected_accounts)} 个账号")
        
        if success_count > 0:
            show_toast(f"成功启动 {success_count}/{len(selected_accounts)} 个账号", "success")
        else:
            show_toast("启动失败", "error")
            
        return success_count
    
    def _parse_account_identifier(self, identifier):
        """解析账号标识符"""
        # 从 "<EMAIL>(CS)" 格式解析
        if '(' in identifier and ')' in identifier:
            username = identifier.split('(')[0]
            role_id = identifier.split('(')[1].rstrip(')')
            return {
                'username': username,
                'role_id': role_id
            }
        return None

    def cleanup(self):
        """清理PowerShell进程"""
        if self.powershell_process and self.powershell_process.poll() is None:
            try:
                self.powershell_process.terminate()
                logger.info("PowerShell进程已终止")
            except Exception as e:
                logger.error(f"终止PowerShell进程失败: {e}")
