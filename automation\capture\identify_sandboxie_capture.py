import cv2
import os.path
from automation.capture.sandboxie_capture import SandboxieCapture
from automation.myutils.configutils import resource_path

class IdentifySandboxieCapture(SandboxieCapture):
    """Sandboxie界面识别捕获器 - 专注于模板匹配"""
    
    def __init__(self, window_name="Sandboxie-Plus"):
        super().__init__(window_name)
        self._load_sandboxie_templates()
    
    def _load_sandboxie_templates(self):
        """加载Sandboxie界面模板"""
        template_path = os.path.join(resource_path, 'template', 'sandboxie')
        
    #     self.icon_sandbox_create = cv2.imread(os.path.join(template_path, 'btn_create_sandbox.png'), cv2.IMREAD_GRAYSCALE)
    #     self.icon_sandbox_start = cv2.imread(os.path.join(template_path, 'btn_start_sandbox.png'), cv2.IMREAD_GRAYSCALE)
    #     self.icon_sandbox_stop = cv2.imread(os.path.join(template_path, 'btn_stop_sandbox.png'), cv2.IMREAD_GRAYSCALE)
    
    # def has_create_button(self):
    #     """检测是否有创建沙盒按钮"""
    #     return self.has_template_icon_in_screen(self.icon_sandbox_create)
    
    # def has_start_button(self):
    #     """检测是否有启动按钮"""
    #     return self.has_template_icon_in_screen(self.icon_sandbox_start)
        
    # def find_sandbox_by_name(self, sandbox_name):
    #     """通过名称查找沙盒位置"""
    #     # OCR识别沙盒列表中的文字
    #     pass
