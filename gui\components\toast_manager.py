from PySide6.QtCore import QObject, QTimer, Qt
from PySide6.QtWidgets import Q<PERSON>abel, QWidget
from PySide6.QtGui import QFont

class ToastManager(QObject):
    """全局Toast通知管理器"""
    
    # 样式常量
    TOAST_STYLES = {
        "success": """
            QLabel {
                background-color: #4CAF50;
                color: white;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
        """,
        "error": """
            QLabel {
                background-color: #F44336;
                color: white;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
        """,
        "warning": """
            QLabel {
                background-color: #FF9800;
                color: white;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
        """,
        "info": """
            QLabel {
                background-color: #2196F3;
                color: white;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
        """
    }
    
    def __init__(self, parent_window):
        super().__init__()
        self.window = parent_window
        self.active_toasts = []
        self.TOAST_DURATION = 3000
        self.TOAST_SPACING = 10
        self.TOAST_BOTTOM_MARGIN = 20
    
    def show_toast(self, message, toast_type="success", duration=None):
        """显示Toast通知
        
        Args:
            message: 消息内容
            toast_type: 类型 ("success", "error", "warning", "info")
            duration: 显示时长(毫秒)，None使用默认值
        """
        if duration is None:
            duration = self.TOAST_DURATION
            
        toast = QLabel(self.window)
        toast.setText(message)
        toast.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置样式
        toast.setStyleSheet(self.TOAST_STYLES.get(toast_type, self.TOAST_STYLES["success"]))
        
        toast.adjustSize()
        self.active_toasts.append(toast)
        self.update_toast_positions()
        
        toast.show()
        toast.raise_()
        
        # 自动隐藏定时器
        timer = QTimer()
        timer.timeout.connect(lambda: self.hide_toast(toast, timer))
        timer.start(duration)
    
    def update_toast_positions(self):
        """更新所有toast的位置，实现堆叠效果"""
        window_rect = self.window.rect()
        current_y = window_rect.height() - self.TOAST_BOTTOM_MARGIN
        
        for toast in reversed(self.active_toasts):
            toast_x = (window_rect.width() - toast.width()) // 2
            current_y -= toast.height()
            toast.move(toast_x, current_y)
            current_y -= self.TOAST_SPACING
    
    def hide_toast(self, toast, timer):
        """隐藏提示条并从列表中移除"""
        if toast in self.active_toasts:
            self.active_toasts.remove(toast)
            self.update_toast_positions()
        
        toast.deleteLater()
        timer.stop()
        timer.deleteLater()
    
    def clear_all_toasts(self):
        """清除所有Toast"""
        for toast in self.active_toasts:
            toast.deleteLater()
        self.active_toasts.clear()

# 全局Toast管理器实例
_toast_manager = None

def init_toast_manager(parent_window):
    """初始化全局Toast管理器"""
    global _toast_manager
    _toast_manager = ToastManager(parent_window)

def get_toast_manager():
    """获取全局Toast管理器"""
    return _toast_manager

def show_toast(message, toast_type="success", duration=None):
    """全局Toast通知快捷方法
    
    Args:
        message: 消息内容
        toast_type: 类型 ("success", "error", "warning", "info")
        duration: 显示时长(毫秒)，None使用默认值
    """
    if _toast_manager:
        _toast_manager.show_toast(message, toast_type, duration)

