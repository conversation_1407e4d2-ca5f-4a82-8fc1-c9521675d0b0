import cv2
import numpy as np
import torch

class ImageRecognizer:
    """图像识别器，负责游戏中的目标检测"""
    
    def __init__(self, config_service):
        self.config_service = config_service
        self.model = None
        self.classes = {}
        self.load_config()
        
    def load_config(self):
        """加载识别配置"""
        recognition_config = self.config_service.get_business_config("recognition")
        model_config = recognition_config.get("model", {})
        
        # 加载类别映射
        self.classes = recognition_config.get("classes", {})
        
        # 加载模型
        model_path = model_config.get("path")
        if model_path and os.path.exists(model_path):
            try:
                self.model = torch.hub.load('ultralytics/yolov5', 'custom', path=model_path)
                self.model.conf = model_config.get("confidence", 0.25)
                print(f"成功加载模型: {model_path}")
            except Exception as e:
                print(f"加载模型失败: {str(e)}")
                self.model = None
    
    def update_model_config(self, updates):
        """更新模型配置"""
        result = self.config_service.update_recognition_model(updates)
        if result:
            self.load_config()
        return result
    
    def get_model_config(self):
        """获取模型配置"""
        return self.config_service.get_recognition_model()
    
    def get_classes(self):
        """获取类别映射"""
        return self.classes
    
    def detect(self, image):
        """检测图像中的目标"""
        if self.model is None:
            return []
        
        results = self.model(image)
        return results.pandas().xyxy[0].to_dict('records')