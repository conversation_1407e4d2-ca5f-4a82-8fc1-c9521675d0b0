<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainForm</class>
 <widget class="QWidget" name="MainForm">
  <property name="windowModality">
   <enum>Qt::WindowModality::NonModal</enum>
  </property>
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1100</width>
    <height>780</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1100</width>
    <height>780</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">/* 全局样式 */
QWidget {
    background-color: #15161B; /* 深蓝灰背景 */
    color: #e0e0e0;            /* 浅灰色文字 */
    font-family: &quot;Microsoft YaHei&quot;, &quot;微软雅黑&quot;, sans-serif;
}

#MainForm {
    border-radius: 12px; /* 圆角 */
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_8">
   <item>
    <widget class="QWidget" name="headFrame" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>38</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_13">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item alignment="Qt::AlignmentFlag::AlignRight">
       <widget class="QFrame" name="headFrame_right">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>30</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::Shape::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Shadow::Raised</enum>
        </property>
        <widget class="QPushButton" name="btn_close">
         <property name="geometry">
          <rect>
           <x>80</x>
           <y>2</y>
           <width>16</width>
           <height>16</height>
          </rect>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>16</height>
          </size>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="styleSheet">
          <string notr="true">#btn_close  {
    background-color: #BC4A59;
    border-radius: 8px; /* 圆角 */
}</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
        <widget class="QPushButton" name="btn_maximize">
         <property name="geometry">
          <rect>
           <x>50</x>
           <y>2</y>
           <width>16</width>
           <height>16</height>
          </rect>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="styleSheet">
          <string notr="true">#btn_maximize  {
    background-color: #2B9D7C;
    border-radius: 8px; /* 圆角 */
}</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
        <widget class="QPushButton" name="btn_minimize">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>2</y>
           <width>16</width>
           <height>16</height>
          </rect>
         </property>
         <property name="font">
          <font>
           <family>Microsoft YaHei</family>
           <pointsize>16</pointsize>
           <bold>false</bold>
          </font>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="styleSheet">
          <string notr="true">#btn_minimize {
    background-color: #CBAF67;
    border-radius: 8px; /* 圆角 */
}

</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="iconSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="InnerWidget" native="true">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>9</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <property name="topMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QWidget" name="LeftFarme" native="true">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">#LeftFarme {
    background-color: #1B1E24; /* 深蓝灰背景 */
    color: #e0e0e0;            /* 浅灰色文字 */
    margin:0px 20px 0 0;
    border: 1px solid #20242A; /* 边框分隔线 */
    border-radius: 12px; /* 圆角 */
}
</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <item>
            <widget class="QLabel" name="appTitle">
             <property name="font">
              <font>
               <family>Microsoft YaHei</family>
               <pointsize>-1</pointsize>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">#appTitle {
    color: #2A9B7A;
    background-color: transparent; 
    font-size: 24px;
    font-weight: bold;
    margin: 20px 0 10px 8px;
    text-align: center;
}</string>
             </property>
             <property name="text">
              <string>BetterElsrift</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QListWidget" name="listWidget">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Microsoft YaHei</family>
               <pointsize>-1</pointsize>
               <bold>false</bold>
              </font>
             </property>
             <property name="cursor" stdset="0">
              <cursorShape>PointingHandCursor</cursorShape>
             </property>
             <property name="toolTipDuration">
              <number>-1</number>
             </property>
             <property name="styleSheet">
              <string notr="true">/* 列表控件样式 */
#listWidget {
    background-color: transparent; /* 透明背景 */
    border: none; /* 无边框 */
    margin:0 20px 0 0;
    font-size: 16px;
    color: #cbd5e0; /* 文字颜色 */
    outline: 0; /* 移除焦点框 */
    show-decoration-selected: 0; /* 移除选中装饰 */
}

/* 列表项样式 */
#listWidget::item {
    height: 50px; /* 项高度 */
    margin: 5px 10px; /* 上下间距5px，左右间距10px */
    padding: 0px 15px; /* 内边距 */
    background-color: transparent; /* 透明背景 */
    border-radius: 8px; /* 圆角 */
}

/* 列表项悬停效果 */
#listWidget::item:hover {
    color: white; /* 选中文字颜色 */
    background-color: #2d3748; /* 悬停背景色 */
}

/* 列表项选中效果 */
#listWidget::item:selected {
    background-color: #2B9D7C; /* 选中背景色 */
    color: white; /* 选中文字颜色 */
    font-weight: bold; /* 加粗 */
}
</string>
             </property>
             <property name="currentRow">
              <number>0</number>
             </property>
             <property name="sortingEnabled">
              <bool>false</bool>
             </property>
             <item>
              <property name="text">
               <string>首页</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>账号</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>功能</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>设置</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>关于</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>日志</string>
              </property>
             </item>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="versionText" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>100</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">#versionText {
    background-color: #1B1E24; /* 深蓝灰背景 */
    margin: 0 20px 10px 8px;
}</string>
             </property>
             <widget class="QLabel" name="label_1">
              <property name="geometry">
               <rect>
                <x>58</x>
                <y>37</y>
                <width>142</width>
                <height>16</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">#label_1 {
    color: #9CA2AE;
    background-color: transparent;
}</string>
              </property>
              <property name="text">
               <string>SandBox-Plus v1.15.12 </string>
              </property>
             </widget>
             <widget class="QPushButton" name="sandBoxSign">
              <property name="geometry">
               <rect>
                <x>40</x>
                <y>40</y>
                <width>10</width>
                <height>10</height>
               </rect>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">#sandBoxSign {
    background-color: #2B9D7C;
    border-radius: 5px; /* 圆角 */
}
</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
             <widget class="QLabel" name="label_2">
              <property name="geometry">
               <rect>
                <x>70</x>
                <y>80</y>
                <width>120</width>
                <height>16</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">#label_2 {
    color: #9CA2AE;
    background-color: transparent;
}</string>
              </property>
              <property name="text">
               <string>BetterElsrift  v0.7.1</string>
              </property>
             </widget>
             <widget class="QLabel" name="system_time">
              <property name="geometry">
               <rect>
                <x>30</x>
                <y>10</y>
                <width>161</width>
                <height>16</height>
               </rect>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>-1</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">#system_time {
    background-color: transparent; 
	color: #9CA2AE;
    font-size: 12px;            /* 字体大小 */
}</string>
              </property>
              <property name="text">
               <string>系统时间：2025/7/15 10:26</string>
              </property>
             </widget>
             <widget class="QPushButton" name="elsriftSign">
              <property name="geometry">
               <rect>
                <x>40</x>
                <y>60</y>
                <width>10</width>
                <height>10</height>
               </rect>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
                <pointsize>16</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">#elsriftSign {
    background-color: #2B9D7C;
    border-radius: 5px; /* 圆角 */
}
</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
             <widget class="QLabel" name="label_3">
              <property name="geometry">
               <rect>
                <x>58</x>
                <y>57</y>
                <width>142</width>
                <height>16</height>
               </rect>
              </property>
              <property name="styleSheet">
               <string notr="true">#label_3 {
    color: #9CA2AE;
    background-color: transparent;
}</string>
              </property>
              <property name="text">
               <string>Elsrift</string>
              </property>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QStackedWidget" name="stackedWidget">
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <bold>false</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="currentIndex">
           <number>5</number>
          </property>
          <widget class="QWidget" name="page_home">
           <layout class="QVBoxLayout" name="verticalLayout">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QScrollArea" name="scrollArea_home">
              <property name="widgetResizable">
               <bool>true</bool>
              </property>
              <widget class="QWidget" name="sah_Layout">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>780</width>
                 <height>705</height>
                </rect>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_9">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QWidget" name="home_head" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>200</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#home_head {
    background-color: #1B1E24; /* 深蓝灰背景 */
    border: 1px solid #20242A; /* 边框分隔线 */
    border-radius: 12px; /* 圆角 */
}</string>
                  </property>
                  <widget class="QPushButton" name="btn_run">
                   <property name="geometry">
                    <rect>
                     <x>280</x>
                     <y>70</y>
                     <width>181</width>
                     <height>61</height>
                    </rect>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei</family>
                     <pointsize>20</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>开始</string>
                   </property>
                  </widget>
                  <widget class="QLabel" name="label_acc_1">
                   <property name="geometry">
                    <rect>
                     <x>10</x>
                     <y>150</y>
                     <width>61</width>
                     <height>31</height>
                    </rect>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei</family>
                     <pointsize>13</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">#label_acc_1 {
    background-color: transparent; 
	color:white；
}</string>
                   </property>
                   <property name="text">
                    <string>账号1：</string>
                   </property>
                  </widget>
                  <widget class="QComboBox" name="comboBox_1">
                   <property name="geometry">
                    <rect>
                     <x>80</x>
                     <y>150</y>
                     <width>261</width>
                     <height>31</height>
                    </rect>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">#comboBox_1 {
    background-color: #282C36; 
	padding-left:10px;
}

    /* 下拉列表样式 */
#comboBox_1 QAbstractItemView {
        background-color: #202429; /* 下拉列表背景色 */
        border: 1px solid #292C33;
        border-radius: 4px;    /* 下拉列表圆角 */
		padding-left:10px;
    }

#comboBox_1 QAbstractItemView::item {
            height: 30px;  /* 项的高度 */
            margin-bottom: 2px;  /* 项之间的间距 */
    }</string>
                   </property>
                   <item>
                    <property name="text">
                     <string>新建项目</string>
                    </property>
                   </item>
                   <item>
                    <property name="text">
                     <string>新建项目</string>
                    </property>
                   </item>
                   <item>
                    <property name="text">
                     <string>新建项目</string>
                    </property>
                   </item>
                   <item>
                    <property name="text">
                     <string>新建项目</string>
                    </property>
                   </item>
                   <item>
                    <property name="text">
                     <string>新建项目</string>
                    </property>
                   </item>
                  </widget>
                  <widget class="QPushButton" name="btn_del">
                   <property name="geometry">
                    <rect>
                     <x>360</x>
                     <y>150</y>
                     <width>60</width>
                     <height>26</height>
                    </rect>
                   </property>
                   <property name="cursor">
                    <cursorShape>PointingHandCursor</cursorShape>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">#btn_del {
	background-color:#D32F2F;
    border-radius: 10px; /* 圆角 */
    font-size: 12px;            /* 字体大小 */
}

#btn_del:hover {
    background-color: #EF5350; /* 深蓝灰背景 */
}</string>
                   </property>
                   <property name="text">
                    <string>删除</string>
                   </property>
                  </widget>
                  <widget class="QPushButton" name="btn_initialize_sandbox">
                   <property name="geometry">
                    <rect>
                     <x>560</x>
                     <y>70</y>
                     <width>101</width>
                     <height>61</height>
                    </rect>
                   </property>
                   <property name="text">
                    <string>初始化</string>
                   </property>
                  </widget>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="home_account" native="true">
                  <property name="styleSheet">
                   <string notr="true">#home_account {
    background-color: #1B1E24; /* 深蓝灰背景 */
    border: 1px solid #20242A; /* 边框分隔线 */
    border-radius: 12px; /* 圆角 */
	margin-top:10px;
}</string>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_14">
                   <property name="leftMargin">
                    <number>9</number>
                   </property>
                   <property name="topMargin">
                    <number>9</number>
                   </property>
                   <property name="rightMargin">
                    <number>9</number>
                   </property>
                   <property name="bottomMargin">
                    <number>9</number>
                   </property>
                   <item>
                    <widget class="QWidget" name="home_account_1" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>60</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#home_account_1 {
    background-color: transparent; /* 深蓝灰背景 */
   	border-top: none;
    border-right: none;
    border-bottom: 2px solid #292C33;
    border-left: none;
	margin:0 10px 0 10px;
}</string>
                     </property>
                     <widget class="QLabel" name="label_nums">
                      <property name="geometry">
                       <rect>
                        <x>10</x>
                        <y>14</y>
                        <width>131</width>
                        <height>31</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>14</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_nums {
    background-color: transparent; 
	color:white；
}</string>
                      </property>
                      <property name="text">
                       <string>⚙️  多开数量：</string>
                      </property>
                     </widget>
                     <widget class="QPushButton" name="btn_add">
                      <property name="geometry">
                       <rect>
                        <x>210</x>
                        <y>14</y>
                        <width>31</width>
                        <height>31</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>-1</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#btn_add {
    color: #FFFFFF;
    background-color: #21252D; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 20px;            /* 字体大小 */
}

#btn_add:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
                      </property>
                      <property name="text">
                       <string>-</string>
                      </property>
                     </widget>
                     <widget class="QPushButton" name="btn_minus">
                      <property name="geometry">
                       <rect>
                        <x>250</x>
                        <y>14</y>
                        <width>31</width>
                        <height>31</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>-1</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#btn_minus {
    color: #FFFFFF;
    background-color: #21252D; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 20px;            /* 字体大小 */
}

#btn_minus:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
                      </property>
                      <property name="text">
                       <string>+</string>
                      </property>
                     </widget>
                     <widget class="QLineEdit" name="lineEdit_count">
                      <property name="geometry">
                       <rect>
                        <x>140</x>
                        <y>14</y>
                        <width>51</width>
                        <height>31</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>11</pointsize>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#lineEdit_count {
	margin:2px;
    background-color: #121317; /* 深蓝灰背景 */
	color:#FFFFFF;
    border: 1px solid #8C8D90;
    border-radius: 10px; /* 圆角 */
}

#lineEdit_count:hover {
    border: 1px solid #2B9D7C;
}

#lineEdit_count:focus {
    border: 1px solid #2B9D7C;
}

</string>
                      </property>
                      <property name="inputMask">
                       <string/>
                      </property>
                      <property name="text">
                       <string>1</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignmentFlag::AlignCenter</set>
                      </property>
                      <property name="readOnly">
                       <bool>false</bool>
                      </property>
                      <property name="placeholderText">
                       <string/>
                      </property>
                     </widget>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_account">
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_10">
            <property name="topMargin">
             <number>4</number>
            </property>
            <item>
             <widget class="QWidget" name="account_head" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>62</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">#account_head {
   	border-top: none;
    border-right: none;
    border-bottom: 3px solid #2D323A;
    border-left: none;
}
</string>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <property name="spacing">
                <number>6</number>
               </property>
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>9</number>
               </property>
               <item>
                <widget class="QWidget" name="account_head_left" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>320</width>
                   <height>0</height>
                  </size>
                 </property>
                 <widget class="QLabel" name="head_info">
                  <property name="geometry">
                   <rect>
                    <x>0</x>
                    <y>0</y>
                    <width>341</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>-1</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_info {
    background-color: transparent; 
	color: #9CA2AE;
    font-size: 13.5px;            /* 字体大小 */
}</string>
                  </property>
                  <property name="text">
                   <string>共 1 个账户（ 0 个启用，1 个启用，0 个冷却中）</string>
                  </property>
                 </widget>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_3">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>199</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QWidget" name="account_head_right" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>320</width>
                   <height>0</height>
                  </size>
                 </property>
                 <widget class="QPushButton" name="btn_refreshState">
                  <property name="geometry">
                   <rect>
                    <x>226</x>
                    <y>2</y>
                    <width>94</width>
                    <height>34</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>-1</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_refreshState {
    color: #FFFFFF;
    background-color: #2B9D7C; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 14px;            /* 字体大小 */
}

#btn_refreshState:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
                  </property>
                  <property name="text">
                   <string>刷新状态</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_addAccount">
                  <property name="geometry">
                   <rect>
                    <x>120</x>
                    <y>2</y>
                    <width>94</width>
                    <height>34</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>-1</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_addAccount {
    color: #FFFFFF;
    background-color: #21252D; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 14px;            /* 字体大小 */
}

#btn_addAccount:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
                  </property>
                  <property name="text">
                   <string>添加账户</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_export">
                  <property name="geometry">
                   <rect>
                    <x>10</x>
                    <y>2</y>
                    <width>94</width>
                    <height>34</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>-1</pointsize>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_export {
    color: #FFFFFF;
    background-color: #21252D; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
    font-size: 14px;            /* 字体大小 */
}

#btn_export:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
                  </property>
                  <property name="text">
                   <string>导出账户</string>
                  </property>
                 </widget>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="account_body">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>60</height>
               </size>
              </property>
              <property name="cursor">
               <cursorShape>ArrowCursor</cursorShape>
              </property>
              <property name="styleSheet">
               <string notr="true">#account_body {
    margin-top:10px;
    background-color: #21252D; /* 灰背景 */
    border-radius: 12px; /* 圆角 */
}
</string>
              </property>
              <property name="frameShape">
               <enum>QFrame::Shape::StyledPanel</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Shadow::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="2,2,2,1">
               <item>
                <widget class="QLabel" name="title_account">
                 <property name="font">
                  <font>
                   <family>Microsoft YaHei</family>
                   <pointsize>-1</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#title_account {
	color:#FFFFFF;
	background-color: transparent; 
    font-size: 14px;            /* 字体大小 */
	margin-left:20px;
}</string>
                 </property>
                 <property name="text">
                  <string>账户信息</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="title_realdata">
                 <property name="font">
                  <font>
                   <family>Microsoft YaHei</family>
                   <pointsize>-1</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#title_realdata {
	color:#FFFFFF;
	background-color: transparent; 
    font-size: 14px;            /* 字体大小 */
}</string>
                 </property>
                 <property name="text">
                  <string>账户实况</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="title_state">
                 <property name="font">
                  <font>
                   <family>Microsoft YaHei</family>
                   <pointsize>-1</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#title_state {
	color:#FFFFFF;
	background-color: transparent; 
    font-size: 14px;            /* 字体大小 */
}</string>
                 </property>
                 <property name="text">
                  <string>账户状态</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="title_operate">
                 <property name="font">
                  <font>
                   <family>Microsoft YaHei</family>
                   <pointsize>-1</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#title_operate {
	color:#FFFFFF;
	background-color: transparent; 
    font-size: 14px;            /* 字体大小 */
	margin-right:20px;
}</string>
                 </property>
                 <property name="text">
                  <string>操作</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QScrollArea" name="scrollArea_account">
              <property name="styleSheet">
               <string notr="true">/* ===== 滚动条样式 ===== */
/* 垂直滚动条 */
#scrollArea_account QScrollBar:vertical {
    border: none;               /* 无边框 */
    background-color: #2A2E36; /* 深蓝灰背景 */
    width: 10px;                /* 滚动条宽度 */
    border-radius: 4px; /* 圆角 */
}

/* 滚动条手柄 */
#scrollArea_account QScrollBar::handle:vertical {
    background: #4A4D56;        /* 手柄颜色 */
    min-height: 20px;            /* 最小高度 */
    border-radius: 5px;          /* 圆角 */
}

/* 滚动条手柄悬停效果 */
#scrollArea_account QScrollBar::handle:vertical:hover {
    background: #5A5D66;        /* 悬停颜色 */
}

/* 滚动条手柄按下效果 */
#scrollArea_account QScrollBar::handle:vertical:pressed {
    background: #5A5D66;        /* 按下颜色 */
}


/* 滚动条按钮（上下箭头） */
#scrollArea_account QScrollBar::add-line:vertical, 
#scrollArea_account QScrollBar::sub-line:vertical,
#scrollArea_account QScrollBar::add-line:horizontal, 
#scrollArea_account QScrollBar::sub-line:horizontal {
	background: none;        /* 按下颜色 */
}

/* 隐藏滚动条槽 */
#scrollArea_account QScrollBar::add-page:vertical, 
#scrollArea_account QScrollBar::sub-page:vertical,
#scrollArea_account QScrollBar::add-page:horizontal, 
#scrollArea_account QScrollBar::sub-page:horizontal {
    background: none;
}</string>
              </property>
              <property name="widgetResizable">
               <bool>true</bool>
              </property>
              <widget class="QWidget" name="saa_Layout">
               <property name="enabled">
                <bool>true</bool>
               </property>
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>570</width>
                 <height>104</height>
                </rect>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_12">
                <item alignment="Qt::AlignmentFlag::AlignTop">
                 <widget class="QWidget" name="accountItem" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>86</height>
                   </size>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="2,2,2,1">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item alignment="Qt::AlignmentFlag::AlignLeft">
                    <widget class="QWidget" name="Item_left" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>222</width>
                       <height>0</height>
                      </size>
                     </property>
                     <widget class="QLabel" name="label_account">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>-2</y>
                        <width>221</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <pointsize>-1</pointsize>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_account {
	color:#2B9D7C;
	background-color: transparent; 
    font-size: 14px;            /* 字体大小 */
}</string>
                      </property>
                      <property name="text">
                       <string><EMAIL></string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_passwordText">
                      <property name="geometry">
                       <rect>
                        <x>38</x>
                        <y>28</y>
                        <width>91</width>
                        <height>20</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#password {
	color:#FFFFFF;
	background-color: transparent; 
    font-size: 14px;            /* 字体大小 */
	margin-left:20px;
}</string>
                      </property>
                      <property name="text">
                       <string>ASDasd123. </string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_role">
                      <property name="geometry">
                       <rect>
                        <x>84</x>
                        <y>60</y>
                        <width>31</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_role {
	color: #9CA2AE;
}</string>
                      </property>
                      <property name="text">
                       <string>角色：</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_roleText">
                      <property name="geometry">
                       <rect>
                        <x>120</x>
                        <y>60</y>
                        <width>81</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>CS</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_pinText">
                      <property name="geometry">
                       <rect>
                        <x>32</x>
                        <y>60</y>
                        <width>51</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>999999</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_pin">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>60</y>
                        <width>31</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_pin {
	color: #9CA2AE;
}</string>
                      </property>
                      <property name="text">
                       <string>PIN：</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_password">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>30</y>
                        <width>31</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_password {
	color: #9CA2AE;
}</string>
                      </property>
                      <property name="text">
                       <string>密码：</string>
                      </property>
                     </widget>
                    </widget>
                   </item>
                   <item alignment="Qt::AlignmentFlag::AlignLeft">
                    <widget class="QWidget" name="Item_middle" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>170</width>
                       <height>0</height>
                      </size>
                     </property>
                     <widget class="QLabel" name="label_runtimeText">
                      <property name="geometry">
                       <rect>
                        <x>60</x>
                        <y>60</y>
                        <width>50</width>
                        <height>20</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true"/>
                      </property>
                      <property name="text">
                       <string>7.2h</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_runtime">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>60</y>
                        <width>51</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_runtime {
	color: #9CA2AE;
}</string>
                      </property>
                      <property name="text">
                       <string>运行时间:</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_lastout">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>32</y>
                        <width>51</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_lastout{
	color: #9CA2AE;
}</string>
                      </property>
                      <property name="text">
                       <string>最后退出:</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_lastout_2">
                      <property name="geometry">
                       <rect>
                        <x>60</x>
                        <y>30</y>
                        <width>112</width>
                        <height>20</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true"/>
                      </property>
                      <property name="text">
                       <string>2025/7/15 11:55</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_lastlogin">
                      <property name="geometry">
                       <rect>
                        <x>0</x>
                        <y>-2</y>
                        <width>51</width>
                        <height>16</height>
                       </rect>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#label_lastlogin {
	color: #9CA2AE;
}</string>
                      </property>
                      <property name="text">
                       <string>最后登录：</string>
                      </property>
                     </widget>
                     <widget class="QLabel" name="label_lastloginText">
                      <property name="geometry">
                       <rect>
                        <x>60</x>
                        <y>-4</y>
                        <width>112</width>
                        <height>20</height>
                       </rect>
                      </property>
                      <property name="font">
                       <font>
                        <family>Microsoft YaHei</family>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true"/>
                      </property>
                      <property name="text">
                       <string>2025/7/15 11:55</string>
                      </property>
                     </widget>
                    </widget>
                   </item>
                   <item alignment="Qt::AlignmentFlag::AlignLeft">
                    <widget class="QLabel" name="label_state">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>80</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>14</pointsize>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#label_state {
	color:#4169E1;
}</string>
                     </property>
                     <property name="text">
                      <string>运行中</string>
                     </property>
                    </widget>
                   </item>
                   <item alignment="Qt::AlignmentFlag::AlignLeft">
                    <widget class="QWidget" name="Item_right" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>80</width>
                       <height>0</height>
                      </size>
                     </property>
                     <widget class="QPushButton" name="btn_delete">
                      <property name="geometry">
                       <rect>
                        <x>14</x>
                        <y>54</y>
                        <width>60</width>
                        <height>26</height>
                       </rect>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#btn_delete {
	background-color:#D32F2F;
    border-radius: 10px; /* 圆角 */
    font-size: 12px;            /* 字体大小 */
}

#btn_delete:hover {
    background-color: #EF5350; /* 深蓝灰背景 */
}</string>
                      </property>
                      <property name="text">
                       <string>删除</string>
                      </property>
                     </widget>
                     <widget class="QPushButton" name="btn_modify">
                      <property name="geometry">
                       <rect>
                        <x>14</x>
                        <y>18</y>
                        <width>60</width>
                        <height>26</height>
                       </rect>
                      </property>
                      <property name="cursor">
                       <cursorShape>PointingHandCursor</cursorShape>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#btn_modify {
	background-color:#3A3E47;
    border-radius: 10px; /* 圆角 */
    font-size: 12px;            /* 字体大小 */
}

#btn_modify:hover {
    background-color: #21252D; /* 深蓝灰背景 */
}</string>
                      </property>
                      <property name="text">
                       <string>修改</string>
                      </property>
                     </widget>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_functions">
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <property name="topMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QScrollArea" name="scrollArea_functions">
              <property name="widgetResizable">
               <bool>true</bool>
              </property>
              <widget class="QWidget" name="saf_Layout">
               <property name="geometry">
                <rect>
                 <x>0</x>
                 <y>0</y>
                 <width>98</width>
                 <height>28</height>
                </rect>
               </property>
               <widget class="QLabel" name="label">
                <property name="geometry">
                 <rect>
                  <x>260</x>
                  <y>300</y>
                  <width>54</width>
                  <height>16</height>
                 </rect>
                </property>
                <property name="text">
                 <string>TextLabel</string>
                </property>
               </widget>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_settings"/>
          <widget class="QWidget" name="page_about">
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <property name="topMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QFrame" name="about_head">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>320</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">#about_head {
    background-color: #1B1E24; /* 深蓝灰背景 */
    margin:10px 10px 15px 8px;
    border: 1px solid #20242A; /* 边框分隔线 */
    border-radius: 12px; /* 圆角 */
}
</string>
              </property>
              <property name="frameShape">
               <enum>QFrame::Shape::StyledPanel</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Shadow::Raised</enum>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_4">
               <item>
                <widget class="QWidget" name="head_1" native="true">
                 <property name="styleSheet">
                  <string notr="true">#head_1 {
    background-color: #1B1E24; /* 深蓝灰背景 */
   	border-top: none;
    border-right: none;
    border-bottom: 1px solid #292C33;
    border-left: none;
    margin:0 15px 15px 15px;
}

</string>
                 </property>
                 <layout class="QHBoxLayout" name="horizontalLayout_2">
                  <item>
                   <widget class="QWidget" name="head_1_left" native="true">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>200</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">#head_1_left {
    background-color: #1B1E24; /* 深蓝灰背景 */
	margin:0 0 10px 0;
}</string>
                    </property>
                    <widget class="QLabel" name="head_1_name">
                     <property name="geometry">
                      <rect>
                       <x>6</x>
                       <y>50</y>
                       <width>181</width>
                       <height>31</height>
                      </rect>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>13</pointsize>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#head_1_name {
    background-color: transparent; 
	color:white；
}</string>
                     </property>
                     <property name="text">
                      <string>BetterElsrift v0.7.1</string>
                     </property>
                    </widget>
                    <widget class="QLabel" name="head_1_about">
                     <property name="geometry">
                      <rect>
                       <x>0</x>
                       <y>0</y>
                       <width>111</width>
                       <height>36</height>
                      </rect>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>20</pointsize>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#head_1_about {
    background-color: transparent; 
	color:white；
}</string>
                     </property>
                     <property name="text">
                      <string>✨ 关于</string>
                     </property>
                    </widget>
                   </widget>
                  </item>
                  <item>
                   <spacer name="horizontalSpacer">
                    <property name="orientation">
                     <enum>Qt::Orientation::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QWidget" name="head_1_right" native="true">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>132</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">#head_1_right {
    background-color: #1B1E24; /* 深蓝灰背景 */
	margin:0 0 10px 0;
}</string>
                    </property>
                    <widget class="QLabel" name="head_1_by">
                     <property name="geometry">
                      <rect>
                       <x>0</x>
                       <y>60</y>
                       <width>132</width>
                       <height>21</height>
                      </rect>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#head_1_by {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                     </property>
                     <property name="text">
                      <string>Developed by SLMagic</string>
                     </property>
                    </widget>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QFrame" name="head_2">
                 <property name="styleSheet">
                  <string notr="true">#head_2 {
    background-color: #1B1E24; /* 深蓝灰背景 */
}</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::Shape::StyledPanel</enum>
                 </property>
                 <property name="frameShadow">
                  <enum>QFrame::Shadow::Raised</enum>
                 </property>
                 <widget class="QLabel" name="head_2_copyrightText">
                  <property name="geometry">
                   <rect>
                    <x>50</x>
                    <y>0</y>
                    <width>211</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>8</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_2_copyrightText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>2025 SLMagic - All rights reserved</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="head_2_emailText">
                  <property name="geometry">
                   <rect>
                    <x>50</x>
                    <y>40</y>
                    <width>61</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>8</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_2_emailText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>SLMagic@</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="head_2_qqText">
                  <property name="geometry">
                   <rect>
                    <x>50</x>
                    <y>80</y>
                    <width>71</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>8</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_2_qqText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>QQ群：111</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="head_2_copyright">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>0</y>
                    <width>16</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>9</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_2_copyright {
    background-color: transparent; 
}</string>
                  </property>
                  <property name="text">
                   <string>©️</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="head_2_email">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>40</y>
                    <width>21</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>10</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_2_email {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>📧</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="head_2_qq">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>80</y>
                    <width>21</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>10</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#head_2_qq {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>👥</string>
                  </property>
                 </widget>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="button_thank">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#button_thank {
	background-color: rgb(31, 45, 48);
    border-radius: 8px; /* 圆角 */
	color: #9CA2AE;
    margin:0 15px 0 15px;
}</string>
                 </property>
                 <property name="text">
                  <string>感谢您的使用和支持❤️</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QWidget" name="about_body" native="true">
              <property name="styleSheet">
               <string notr="true">#about_body {
    background-color: #1B1E24; /* 深蓝灰背景 */
    color: #e0e0e0;            /* 浅灰色文字 */
    margin:0 10px 80px 8px;
    border: 1px solid #20242A; /* 边框分隔线 */
    border-radius: 12px; /* 圆角 */
}

</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_11">
               <item>
                <widget class="QWidget" name="body_1" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#body_1 {
    background-color: transparent; 
}</string>
                 </property>
                 <layout class="QHBoxLayout" name="horizontalLayout_3">
                  <item>
                   <widget class="QWidget" name="body_1_left" native="true">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>160</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">#body_1_left {
	background-color: transparent; 
}</string>
                    </property>
                    <widget class="QLabel" name="body_TitleText">
                     <property name="geometry">
                      <rect>
                       <x>20</x>
                       <y>0</y>
                       <width>140</width>
                       <height>31</height>
                      </rect>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft YaHei</family>
                       <pointsize>18</pointsize>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#body_TitleText {
    background-color: transparent; 
	color:white；
}</string>
                     </property>
                     <property name="text">
                      <string>📚 相关文档</string>
                     </property>
                    </widget>
                   </widget>
                  </item>
                  <item>
                   <spacer name="horizontalSpacer_2">
                    <property name="orientation">
                     <enum>Qt::Orientation::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>393</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QWidget" name="body_1_right" native="true">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>160</width>
                      <height>0</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">#body_1_right {
	background-color: transparent; 
}</string>
                    </property>
                    <widget class="QLabel" name="body_copyText">
                     <property name="geometry">
                      <rect>
                       <x>0</x>
                       <y>12</y>
                       <width>160</width>
                       <height>20</height>
                      </rect>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">#body_copyText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                     </property>
                     <property name="text">
                      <string>左键点击跳转，右键点击复制</string>
                     </property>
                    </widget>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QWidget" name="body_2" native="true">
                 <property name="styleSheet">
                  <string notr="true">#body_2 {
    background-color: transparent; 

}</string>
                 </property>
                 <widget class="QLabel" name="body_bugText">
                  <property name="geometry">
                   <rect>
                    <x>30</x>
                    <y>130</y>
                    <width>601</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>10</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#body_bugText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>🤖  版本BUG通知文档：</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="body_documentText">
                  <property name="geometry">
                   <rect>
                    <x>30</x>
                    <y>10</y>
                    <width>601</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>10</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#body_documentText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>📖  官方文档：</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="body_updateLogText">
                  <property name="geometry">
                   <rect>
                    <x>30</x>
                    <y>50</y>
                    <width>601</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>10</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#body_updateLogText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>📜  更新日志：</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="body_HelpText">
                  <property name="geometry">
                   <rect>
                    <x>30</x>
                    <y>90</y>
                    <width>601</width>
                    <height>20</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <pointsize>10</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#body_HelpText {
    background-color: transparent; 
	color: #9CA2AE;
}</string>
                  </property>
                  <property name="text">
                   <string>💡  帮助，问题文档：</string>
                  </property>
                 </widget>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_logs">
           <property name="styleSheet">
            <string notr="true">#page_logs {
    background-color: #1B1E24; /* 深蓝灰背景 */
    color: #e0e0e0;            /* 浅灰色文字 */
    border: 1px solid #20242A; /* 边框分隔线 */
    border-radius: 12px; /* 圆角 */
}
</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <widget class="QFrame" name="logs_head">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>80</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">#logs_head {
    background-color: #1B1E24; /* 深蓝灰背景 */
}
</string>
              </property>
              <property name="frameShape">
               <enum>QFrame::Shape::StyledPanel</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Shadow::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QWidget" name="logs_head_left" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>420</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#logs_head_left {
    background-color: transparent; 
}</string>
                 </property>
                 <widget class="QPushButton" name="btn_info">
                  <property name="geometry">
                   <rect>
                    <x>26</x>
                    <y>22</y>
                    <width>68</width>
                    <height>26</height>
                   </rect>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_info {
	color: #9E9E9E;
	background-color: #1B1E24; 
	border: 1px solid #9E9E9E;
	border-radius: 10px;
}

#btn_info:hover {
	background-color: rgba(158, 158, 158, 0.2);
}



</string>
                  </property>
                  <property name="text">
                   <string>信息</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_error">
                  <property name="geometry">
                   <rect>
                    <x>186</x>
                    <y>22</y>
                    <width>68</width>
                    <height>26</height>
                   </rect>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_error {
	color: #FF6B6B;
	background-color: #1B1E24;
	border: 1px solid #FF6B6B;
	border-radius: 10px;
}

#btn_error:hover {
	background-color: rgba(255, 107, 107, 0.2);
}</string>
                  </property>
                  <property name="text">
                   <string>错误</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_warning">
                  <property name="geometry">
                   <rect>
                    <x>106</x>
                    <y>22</y>
                    <width>68</width>
                    <height>26</height>
                   </rect>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_warning {
	color: #FFE24D;
	background-color: #1B1E24;
	border: 1px solid #FFE24D;
	border-radius: 10px;
}

#btn_warning:hover {
	background-color: rgba(255, 226, 77, 0.2);
}
</string>
                  </property>
                  <property name="text">
                   <string>警告</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_debug">
                  <property name="geometry">
                   <rect>
                    <x>268</x>
                    <y>22</y>
                    <width>68</width>
                    <height>26</height>
                   </rect>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_debug {
	color: #3875FF;
	background-color: #1B1E24;
	border: 1px solid #3875FF;
	border-radius: 10px;
}

#btn_debug:hover {
	background-color: rgba(56, 123, 255, 0.2);
}</string>
                  </property>
                  <property name="text">
                   <string>调试</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_critical">
                  <property name="geometry">
                   <rect>
                    <x>348</x>
                    <y>22</y>
                    <width>68</width>
                    <height>26</height>
                   </rect>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_critical {
	color: #FF4757;
	background-color: #1B1E24;
	border: 1px solid #FF4757;
	border-radius: 10px;
}

#btn_critical:hover {
	background-color: rgba(255, 71, 87, 0.2);
}</string>
                  </property>
                  <property name="text">
                   <string>严重</string>
                  </property>
                 </widget>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_10">
                 <property name="orientation">
                  <enum>Qt::Orientation::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>96</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QWidget" name="logs_head_right" native="true">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>222</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#logs_head_right {
    background-color: transparent; 
}</string>
                 </property>
                 <widget class="QPushButton" name="btn_refreshLogs">
                  <property name="geometry">
                   <rect>
                    <x>126</x>
                    <y>14</y>
                    <width>94</width>
                    <height>36</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_refreshLogs {
	color: #FFFFFF;
    background-color: #2B9D7C; /* 深蓝灰背景 */
    border-radius: 10px; /* 圆角 */
}

#btn_refreshLogs:hover {
    background-color: #34B892; /* 深蓝灰背景 */
}</string>
                  </property>
                  <property name="text">
                   <string>刷新日志</string>
                  </property>
                 </widget>
                 <widget class="QPushButton" name="btn_pathLogs">
                  <property name="geometry">
                   <rect>
                    <x>0</x>
                    <y>14</y>
                    <width>110</width>
                    <height>34</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei</family>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="cursor">
                   <cursorShape>PointingHandCursor</cursorShape>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">#btn_pathLogs {
	color: #FFFFFF;
    background-color: #1B1E24; /* 深蓝灰背景 */
    border: 1px solid #292C33;
    border-radius: 10px; /* 圆角 */
}

#btn_pathLogs:hover {
    background-color: #D6B16A;
}
</string>
                  </property>
                  <property name="text">
                   <string>日志目录</string>
                  </property>
                 </widget>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="logs_body">
              <property name="cursor">
               <cursorShape>ArrowCursor</cursorShape>
              </property>
              <property name="styleSheet">
               <string notr="true">#logs_body {
    background-color: #1B1E24; /* 深蓝灰背景 */
}
</string>
              </property>
              <property name="frameShape">
               <enum>QFrame::Shape::StyledPanel</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Shadow::Raised</enum>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_7">
               <item>
                <widget class="QPlainTextEdit" name="logs_text">
                 <property name="font">
                  <font>
                   <family>Consolas</family>
                   <pointsize>-1</pointsize>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="cursor" stdset="0">
                  <cursorShape>ArrowCursor</cursorShape>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">/* ===== 基础样式 ===== */
#logs_text {
    /* 背景和边框 */
    background-color: #1B1E24; /* 深蓝灰背景 */
	/* 选中文本的样式 */
    selection-background-color: #2B9D7C;
	font-family: &quot;Consolas&quot;;
    
/* ===== 文本内容样式 ===== */
    /* 文本样式 */
    color: #9E9E9E;             /* 文本颜色 */
    font-size: 14px;            /* 字体大小 */
    
    /* 文本边距 */
    padding-right: 15px;         /* 右内边距 */
    padding-bottom: 20px;        /* 下内边距 */
    padding-left: 15px;          /* 左内边距 */
    
       /* 字体和行间距 */
    font-family: &quot;Consolas&quot;;
    font-size: 14px;
    line-height: 5;                    /* 行间距倍数 */
    
    /* 字符间距 */
    letter-spacing: 1px;               
}

/* ===== 滚动条样式 ===== */
/* 垂直滚动条 */
#logs_text QScrollBar:vertical {
    border: none;               /* 无边框 */
    background-color: #2A2E36; /* 深蓝灰背景 */
    width: 10px;                /* 滚动条宽度 */
    border-radius: 4px; /* 圆角 */
}

/* 滚动条手柄 */
#logs_text QScrollBar::handle:vertical {
    background: #4A4D56;        /* 手柄颜色 */
    min-height: 20px;            /* 最小高度 */
    border-radius: 5px;          /* 圆角 */
}

/* 滚动条手柄悬停效果 */
#logs_text QScrollBar::handle:vertical:hover {
    background: #5A5D66;        /* 悬停颜色 */
}

/* 滚动条手柄按下效果 */
#logs_text QScrollBar::handle:vertical:pressed {
    background: #5A5D66;        /* 按下颜色 */
}

/* 滚动条按钮（上下箭头） */
#logs_text QScrollBar::add-line:vertical, 
#logs_text QScrollBar::sub-line:vertical,
#logs_text QScrollBar::add-line:horizontal, 
#logs_text QScrollBar::sub-line:horizontal {
	background: none;        /* 按下颜色 */
}

/* 隐藏滚动条槽 */
#logs_text QScrollBar::add-page:vertical, 
#logs_text QScrollBar::sub-page:vertical,
#logs_text QScrollBar::add-page:horizontal, 
#logs_text QScrollBar::sub-page:horizontal {
    background: none;
}
</string>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                 <property name="plainText">
                  <string>2025-07-15 01:01:11 [INFO] - 日志系统初始化完成，日志文件: C:\Users\<USER>\AppData\Roaming\YCursor\logs\2025-07-15.log
2025-07-15 01:01:11 [INFO] - YCursor已启动
2025-07-15 01:01:11 [INFO] - Python版本: 3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]
2025-07-15 01:01:11 [INFO] - 平台信息: win32
2025-07-15 01:01:11 [INFO] - 已为Windows Python 3.11+配置asyncio事件循环策略
2025-07-15 01:01:11 [INFO] - 应用程序图标已设置为: C:\PROGRA~1\YCursor\icons\yan.ico
2025-07-15 01:01:20 [INFO] - 状态栏版本更新为: Cursor 1.2.1 | YCursor 4.6.2
2025-07-15 01:01:21 [INFO] - 显示保存当前登录账户按钮（自动隐藏=False, email=<EMAIL>）
2025-07-15 01:01:21 [INFO] - 已添加保存当前登录账户按钮到布局
2025-07-15 01:01:21 [INFO] - 刷新功能页面: auto_hide=True, is_version_known=False, should_hide=False
2025-07-15 01:01:21 [INFO] - 自定义路径卡片可见性设置为: True
2025-07-15 01:01:22 [INFO] - 切换页面: 从 首页 到 账户管理
2025-07-15 01:01:22 [INFO] - 首次切换至账户管理页面，将加载数据
2025-07-15 01:01:22 [INFO] - 执行首次加载/强制刷新逻辑
2025-07-15 01:01:23 [INFO] - 切换页面: 从 账户管理 到 功能
2025-07-15 01:01:23 [INFO] - 刷新功能页面: auto_hide=True, is_version_known=True, should_hide=True
2025-07-15 01:01:23 [INFO] - 自定义路径卡片可见性设置为: False
2025-07-15 01:01:23 [INFO] - 显示保存当前登录账户按钮（自动隐藏=False, email=<EMAIL>）
2025-07-15 01:01:23 [INFO] - 切换页面: 从 功能 到 设置
2025-07-15 01:01:24 [INFO] - 切换页面: 从 设置 到 关于
2025-07-15 01:01:24 [INFO] - 切换页面: 从 设置 到 日志
2025-07-15 01:01:25 [INFO] - 切换页面: 从 关于 到 日志
2025-07-15 01:01:57 [INFO] - YCursor正在关闭...
2025-07-15 01:01:57 [INFO] - YCursor已关闭
2025-07-15 01:13:42 [INFO] - 日志系统初始化完成，日志文件: C:\Users\<USER>\AppData\Roaming\YCursor\logs\2025-07-15.log
2025-07-15 01:13:42 [INFO] - YCursor已启动
2025-07-15 01:13:42 [INFO] - Python版本: 3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]
2025-07-15 01:13:42 [INFO] - 平台信息: win32
2025-07-15 01:13:42 [INFO] - 已为Windows Python 3.11+配置asyncio事件循环策略
2025-07-15 01:13:42 [INFO] - 应用程序图标已设置为: C:\PROGRA~1\YCursor\icons\yan.ico
2025-07-15 01:13:51 [INFO] - 状态栏版本更新为: Cursor 1.2.1 | YCursor 4.6.2
2025-07-15 01:13:51 [INFO] - 显示保存当前登录账户按钮（自动隐藏=False, email=<EMAIL>）
2025-07-15 01:13:51 [INFO] - 已添加保存当前登录账户按钮到布局
2025-07-15 01:13:51 [INFO] - 刷新功能页面: auto_hide=True, is_version_known=False, should_hide=False
2025-07-15 01:13:51 [INFO] - 自定义路径卡片可见性设置为: True
2025-07-15 01:13:56 [INFO] - 切换页面: 从 首页 到 账户管理
2025-07-15 01:13:56 [INFO] - 首次切换至账户管理页面，将加载数据
2025-07-15 01:13:56 [INFO] - 执行首次加载/强制刷新逻辑
2025-07-15 01:13:56 [INFO] - 切换页面: 从 账户管理 到 功能
2025-07-15 01:13:56 [INFO] - 刷新功能页面: auto_hide=True, is_version_known=True, should_hide=True
2025-07-15 01:13:56 [INFO] - 自定义路径卡片可见性设置为: False
2025-07-15 01:13:56 [INFO] - 显示保存当前登录账户按钮（自动隐藏=False, email=<EMAIL>）
2025-07-15 01:13:57 [INFO] - 切换页面: 从 功能 到 账户管理
2025-07-15 01:13:57 [INFO] - 首次切换至账户管理页面，将加载数据
2025-07-15 01:13:57 [INFO] - 执行首次加载/强制刷新逻辑
2025-07-15 01:14:06 [INFO] - 刷新前的账户数量: 1, 最新账户: <EMAIL>
2025-07-15 01:14:09 [INFO] - 切换页面: 从 账户管理 到 设置
2025-07-15 01:14:18 [INFO] - 切换页面: 从 设置 到 关于
2025-07-15 01:14:20 [INFO] - 切换页面: 从 关于 到 设置
2025-07-15 01:14:22 [INFO] - 切换页面: 从 设置 到 关于
2025-07-15 01:14:25 [INFO] - 切换页面: 从 关于 到 日志
2025-07-15 02:22:56 [INFO] - YCursor正在关闭...
2025-07-15 02:22:56 [INFO] - YCursor已关闭
2025-07-15 02:23:50 [INFO] - 日志系统初始化完成，日志文件: C:\Users\<USER>\AppData\Roaming\YCursor\logs\2025-07-15.log
2025-07-15 02:23:50 [INFO] - YCursor已启动
2025-07-15 02:23:50 [INFO] - Python版本: 3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]
2025-07-15 02:23:50 [INFO] - 平台信息: win32
2025-07-15 02:23:50 [INFO] - 已为Windows Python 3.11+配置asyncio事件循环策略
2025-07-15 02:23:50 [INFO] - 应用程序图标已设置为: C:\PROGRA~1\YCursor\icons\yan.ico
2025-07-15 02:24:00 [INFO] - 状态栏版本更新为: Cursor 1.2.1 | YCursor 4.6.2
2025-07-15 02:24:00 [INFO] - 显示保存当前登录账户按钮（自动隐藏=False, email=<EMAIL>）
2025-07-15 02:24:00 [INFO] - 已添加保存当前登录账户按钮到布局
2025-07-15 02:24:00 [INFO] - 刷新功能页面: auto_hide=True, is_version_known=False, should_hide=False
2025-07-15 02:24:00 [INFO] - 自定义路径卡片可见性设置为: True
2025-07-15 02:36:42 [INFO] - 切换页面: 从 首页 到 日志
2025-07-15 03:57:45 [INFO] - YCursor正在关闭...
2025-07-15 03:57:45 [INFO] - YCursor已关闭
2025-07-15 03:58:01 [INFO] - 日志系统初始化完成，日志文件: C:\Users\<USER>\AppData\Roaming\YCursor\logs\2025-07-15.log
2025-07-15 03:58:02 [INFO] - YCursor已启动
2025-07-15 03:58:02 [INFO] - Python版本: 3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]
2025-07-15 03:58:02 [INFO] - 平台信息: win32
2025-07-15 03:58:02 [INFO] - 已为Windows Python 3.11+配置asyncio事件循环策略
2025-07-15 03:58:02 [INFO] - 应用程序图标已设置为: C:\PROGRA~1\YCursor\icons\yan.ico
2025-07-15 03:58:11 [INFO] - 状态栏版本更新为: Cursor 1.2.1 | YCursor 4.6.2
2025-07-15 03:58:11 [INFO] - 显示保存当前登录账户按钮（自动隐藏=False, email=<EMAIL>）
2025-07-15 03:58:11 [INFO] - 已添加保存当前登录账户按钮到布局
2025-07-15 03:58:11 [INFO] - 刷新功能页面: auto_hide=True, is_version_known=False, should_hide=False
2025-07-15 03:58:11 [INFO] - 自定义路径卡片可见性设置为: True
2025-07-15 03:58:34 [INFO] - 切换页面: 从 首页 到 日志</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
