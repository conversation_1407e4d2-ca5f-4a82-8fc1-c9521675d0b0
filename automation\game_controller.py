import pyautogui
import time
from core.utils.logger import Logger

class GameController:
    """游戏控制器，负责与游戏交互"""
    
    def __init__(self, config):
        self.config = config
        self.logger = Logger("GameController")
    
    def click(self, x, y, delay=0.1):
        """在指定位置点击"""
        pyautogui.click(x, y)
        time.sleep(delay)
        
    def press_key(self, key, delay=0.1):
        """按下指定按键"""
        pyautogui.press(key)
        time.sleep(delay)
    
    def move_to(self, x, y, duration=0.2):
        """移动鼠标到指定位置"""
        pyautogui.moveTo(x, y, duration=duration)