import os
import sys
# config_name = 'config.yaml'
application_path = '.'
if getattr(sys, 'frozen', False):
    application_path = os.path.dirname(sys.executable)
    application_path = os.path.join(application_path, '_internal')
elif __file__:
    application_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

PROJECT_PATH = application_path
resource_path = os.path.join(PROJECT_PATH, 'resources')

class WindowsConfig():
    KEY_GAME_PATH = 'game_path'
    KEY_WINDOW_NAME = 'window_name'

class DebugConfig():
    KEY_DEBUG_ENABLE = 'debug_enable'
