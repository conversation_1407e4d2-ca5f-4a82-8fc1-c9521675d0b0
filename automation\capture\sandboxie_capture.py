from automation.capture.windowcapture import WindowCapture

class SandboxieCapture(WindowCapture):
    """Sandboxie窗口捕获器 - 专注于窗口捕获和区域定义"""
    
    def __init__(self, window_name="Sandboxie-Plus"):
        super().__init__(window_name)  
        self._update_rect()  # 改为单下划线

    # def get_sandbox_list_area(self):
    #     """获取沙盒列表区域"""
    #     self.update_screenshot_if_none()
    #     return self.screenshot[100:400, 50:300]
        
    # def get_toolbar_area(self):
    #     """获取工具栏区域"""
    #     self.update_screenshot_if_none()
    #     return self.screenshot[20:80, 0:self.w]
        
    # def click_create_sandbox_menu(self):
    #     """点击创建沙盒菜单"""
    #     return self.bg_click_position(30, 53)



